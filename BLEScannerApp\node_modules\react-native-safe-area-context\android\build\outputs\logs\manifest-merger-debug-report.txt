-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Desktop\app for beacons\BeaconL01\BLEScannerApp\node_modules\react-native-safe-area-context\android\src\main\AndroidManifest.xml:2:1-5:12
INJECTED from C:\Users\<USER>\Desktop\app for beacons\BeaconL01\BLEScannerApp\node_modules\react-native-safe-area-context\android\src\main\AndroidManifest.xml:2:1-5:12
	package
		ADDED from C:\Users\<USER>\Desktop\app for beacons\BeaconL01\BLEScannerApp\node_modules\react-native-safe-area-context\android\src\main\AndroidManifest.xml:4:3-42
		INJECTED from C:\Users\<USER>\Desktop\app for beacons\BeaconL01\BLEScannerApp\node_modules\react-native-safe-area-context\android\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\app for beacons\BeaconL01\BLEScannerApp\node_modules\react-native-safe-area-context\android\src\main\AndroidManifest.xml:3:2-60
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\app for beacons\BeaconL01\BLEScannerApp\node_modules\react-native-safe-area-context\android\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\app for beacons\BeaconL01\BLEScannerApp\node_modules\react-native-safe-area-context\android\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\app for beacons\BeaconL01\BLEScannerApp\node_modules\react-native-safe-area-context\android\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\app for beacons\BeaconL01\BLEScannerApp\node_modules\react-native-safe-area-context\android\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\app for beacons\BeaconL01\BLEScannerApp\node_modules\react-native-safe-area-context\android\src\main\AndroidManifest.xml
