  Application android.app  DefaultReactActivityDelegate android.app.Activity  
fabricEnabled android.app.Activity  BeaconXPackage android.app.Application  Boolean android.app.Application  BuildConfig android.app.Application  DefaultReactNativeHost android.app.Application  List android.app.Application  OpenSourceMergedSoMapping android.app.Application  PackageList android.app.Application  ReactPackage android.app.Application  SoLoader android.app.Application  String android.app.Application  apply android.app.Application  getDefaultReactHost android.app.Application  load android.app.Application  onCreate android.app.Application  Context android.content  BeaconXPackage android.content.Context  Boolean android.content.Context  BuildConfig android.content.Context  DefaultReactActivityDelegate android.content.Context  DefaultReactNativeHost android.content.Context  List android.content.Context  OpenSourceMergedSoMapping android.content.Context  PackageList android.content.Context  ReactPackage android.content.Context  SoLoader android.content.Context  String android.content.Context  apply android.content.Context  
fabricEnabled android.content.Context  getDefaultReactHost android.content.Context  load android.content.Context  BeaconXPackage android.content.ContextWrapper  Boolean android.content.ContextWrapper  BuildConfig android.content.ContextWrapper  DefaultReactActivityDelegate android.content.ContextWrapper  DefaultReactNativeHost android.content.ContextWrapper  List android.content.ContextWrapper  OpenSourceMergedSoMapping android.content.ContextWrapper  PackageList android.content.ContextWrapper  ReactPackage android.content.ContextWrapper  SoLoader android.content.ContextWrapper  String android.content.ContextWrapper  applicationContext android.content.ContextWrapper  apply android.content.ContextWrapper  
fabricEnabled android.content.ContextWrapper  getDefaultReactHost android.content.ContextWrapper  load android.content.ContextWrapper  DefaultReactActivityDelegate  android.view.ContextThemeWrapper  
fabricEnabled  android.view.ContextThemeWrapper  DefaultReactActivityDelegate #androidx.activity.ComponentActivity  
fabricEnabled #androidx.activity.ComponentActivity  DefaultReactActivityDelegate (androidx.appcompat.app.AppCompatActivity  
fabricEnabled (androidx.appcompat.app.AppCompatActivity  DefaultReactActivityDelegate #androidx.core.app.ComponentActivity  ReactActivityDelegate #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  
fabricEnabled #androidx.core.app.ComponentActivity  DefaultReactActivityDelegate &androidx.fragment.app.FragmentActivity  
fabricEnabled &androidx.fragment.app.FragmentActivity  Application com.blescannerapp  
BeaconXModule com.blescannerapp  BeaconXPackage com.blescannerapp  Boolean com.blescannerapp  BuildConfig com.blescannerapp  DefaultReactActivityDelegate com.blescannerapp  DefaultReactNativeHost com.blescannerapp  List com.blescannerapp  MainActivity com.blescannerapp  MainApplication com.blescannerapp  NativeModule com.blescannerapp  OpenSourceMergedSoMapping com.blescannerapp  PackageList com.blescannerapp  
ReactActivity com.blescannerapp  ReactActivityDelegate com.blescannerapp  ReactApplication com.blescannerapp  ReactApplicationContext com.blescannerapp  	ReactHost com.blescannerapp  ReactNativeHost com.blescannerapp  ReactPackage com.blescannerapp  SoLoader com.blescannerapp  String com.blescannerapp  ViewManager com.blescannerapp  apply com.blescannerapp  	emptyList com.blescannerapp  
fabricEnabled com.blescannerapp  getDefaultReactHost com.blescannerapp  listOf com.blescannerapp  load com.blescannerapp  
BeaconXModule  com.blescannerapp.BeaconXPackage  	emptyList  com.blescannerapp.BeaconXPackage  listOf  com.blescannerapp.BeaconXPackage  DEBUG com.blescannerapp.BuildConfig  IS_HERMES_ENABLED com.blescannerapp.BuildConfig  IS_NEW_ARCHITECTURE_ENABLED com.blescannerapp.BuildConfig  DefaultReactActivityDelegate com.blescannerapp.MainActivity  
fabricEnabled com.blescannerapp.MainActivity  mainComponentName com.blescannerapp.MainActivity  BeaconXPackage !com.blescannerapp.MainApplication  BuildConfig !com.blescannerapp.MainApplication  OpenSourceMergedSoMapping !com.blescannerapp.MainApplication  PackageList !com.blescannerapp.MainApplication  SoLoader !com.blescannerapp.MainApplication  applicationContext !com.blescannerapp.MainApplication  apply !com.blescannerapp.MainApplication  getDefaultReactHost !com.blescannerapp.MainApplication  load !com.blescannerapp.MainApplication  reactNativeHost !com.blescannerapp.MainApplication  PackageList com.facebook.react  
ReactActivity com.facebook.react  ReactActivityDelegate com.facebook.react  ReactApplication com.facebook.react  	ReactHost com.facebook.react  ReactNativeHost com.facebook.react  ReactPackage com.facebook.react  packages com.facebook.react.PackageList  DefaultReactActivityDelegate  com.facebook.react.ReactActivity  
fabricEnabled  com.facebook.react.ReactActivity  BeaconXPackage "com.facebook.react.ReactNativeHost  BuildConfig "com.facebook.react.ReactNativeHost  PackageList "com.facebook.react.ReactNativeHost  apply "com.facebook.react.ReactNativeHost  NativeModule com.facebook.react.bridge  ReactApplicationContext com.facebook.react.bridge  DefaultReactActivityDelegate com.facebook.react.defaults  DefaultReactNativeHost com.facebook.react.defaults  
fabricEnabled <com.facebook.react.defaults.DefaultNewArchitectureEntryPoint  load <com.facebook.react.defaults.DefaultNewArchitectureEntryPoint  getDefaultReactHost ,com.facebook.react.defaults.DefaultReactHost  OpenSourceMergedSoMapping com.facebook.react.soloader  ViewManager com.facebook.react.uimanager  SoLoader com.facebook.soloader  init com.facebook.soloader.SoLoader  	ArrayList 	java.util  BeaconXPackage java.util.ArrayList  add java.util.ArrayList  apply java.util.ArrayList  	Function1 kotlin  apply kotlin  List kotlin.collections  	emptyList kotlin.collections  listOf kotlin.collections                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     