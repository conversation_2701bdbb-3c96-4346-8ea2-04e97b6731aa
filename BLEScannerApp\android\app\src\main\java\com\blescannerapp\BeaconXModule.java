
package com.blescannerapp;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothManager;
import android.content.Context;
import android.os.ParcelUuid;
import android.util.Log;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.modules.core.DeviceEventManagerModule;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

// Import Nordic Scanner invece del scanner Android standard
import no.nordicsemi.android.support.v18.scanner.BluetoothLeScannerCompat;
import no.nordicsemi.android.support.v18.scanner.ScanCallback;
import no.nordicsemi.android.support.v18.scanner.ScanFilter;
import no.nordicsemi.android.support.v18.scanner.ScanResult;
import no.nordicsemi.android.support.v18.scanner.ScanRecord;
import no.nordicsemi.android.support.v18.scanner.ScanSettings;

public class BeaconXModule extends ReactContextBaseJavaModule {
    private static final String TAG = "BeaconXModule";
    private ReactApplicationContext reactContext;
    private BluetoothAdapter bluetoothAdapter;
    private boolean isScanning = false;

    public BeaconXModule(ReactApplicationContext reactContext) {
        super(reactContext);
        this.reactContext = reactContext;

        Log.d(TAG, "BeaconXModule constructor called");

        BluetoothManager bluetoothManager = (BluetoothManager) reactContext.getSystemService(Context.BLUETOOTH_SERVICE);
        bluetoothAdapter = bluetoothManager.getAdapter();

        Log.d(TAG, "BluetoothAdapter initialized: " + (bluetoothAdapter != null ? "SUCCESS" : "FAILED"));
    }

    @Override
    public String getName() {
        return "BeaconXModule";
    }

    private void sendEvent(String eventName, WritableMap params) {
        reactContext
            .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
            .emit(eventName, params);
    }

    private ScanCallback scanCallback = new ScanCallback() {
        @Override
        public void onScanResult(int callbackType, ScanResult result) {
            super.onScanResult(callbackType, result);
            Log.d(TAG, "onScanResult called - device found!");

            // Filtri come nell'SDK ufficiale
            byte[] scanRecord = result.getScanRecord() != null ? result.getScanRecord().getBytes() : null;
            int rssi = result.getRssi();
            String deviceName = result.getScanRecord() != null ? result.getScanRecord().getDeviceName() : null;
            String deviceAddress = result.getDevice().getAddress();

            Log.d(TAG, "Raw scan result: " + deviceAddress + " (" + deviceName + ") RSSI: " + rssi +
                  " ScanRecord length: " + (scanRecord != null ? scanRecord.length : 0));

            // Filtra risultati invalidi come fa l'SDK ufficiale
            if (scanRecord == null || scanRecord.length == 0 || rssi == 127) {
                Log.d(TAG, "Filtered out device " + deviceAddress + " - invalid scan record or RSSI");
                return;
            }

            // Crea DeviceInfo come fa l'SDK ufficiale
            DeviceInfo deviceInfo = createDeviceInfo(result);
            if (deviceInfo == null) return;
            
            // Usa la stessa logica di parsing dell'SDK
            BeaconXInfo beaconXInfo = parseBeaconXInfo(deviceInfo);
            if (beaconXInfo == null) return;
            
            Log.d(TAG, "Found BeaconX device: " + beaconXInfo.name + " (" + beaconXInfo.mac + ")");
            
            WritableMap device = Arguments.createMap();
            device.putString("name", beaconXInfo.name != null ? beaconXInfo.name : "BeaconX Pro");
            device.putString("mac", beaconXInfo.mac);
            device.putInt("rssi", beaconXInfo.rssi);
            device.putInt("battery", beaconXInfo.battery);
            device.putInt("lockState", beaconXInfo.lockState);
            device.putInt("connectState", beaconXInfo.connectState);
            device.putInt("ambientLightState", beaconXInfo.ambientLightState);
            device.putInt("tamperState", beaconXInfo.tamperState);
            device.putString("scanRecord", beaconXInfo.scanRecord);
            
            sendEvent("onDeviceFound", device);
        }

        @Override
        public void onBatchScanResults(List<ScanResult> results) {
            super.onBatchScanResults(results);
            for (ScanResult result : results) {
                onScanResult(ScanSettings.CALLBACK_TYPE_ALL_MATCHES, result);
            }
        }

        @Override
        public void onScanFailed(int errorCode) {
            super.onScanFailed(errorCode);
            Log.e(TAG, "Scan failed with error: " + errorCode);
            isScanning = false;
            sendEvent("onScanStop", null);
        }
    };

    @ReactMethod
    public void startScan(Promise promise) {
        Log.d(TAG, "startScan method called");
        try {
            if (bluetoothAdapter == null) {
                Log.e(TAG, "BluetoothAdapter is null");
                promise.reject("BLUETOOTH_ERROR", "Bluetooth adapter is null");
                return;
            }

            if (!bluetoothAdapter.isEnabled()) {
                Log.e(TAG, "Bluetooth is not enabled");
                promise.reject("BLUETOOTH_ERROR", "Bluetooth is not enabled");
                return;
            }

            Log.d(TAG, "Bluetooth checks passed");

            if (isScanning) {
                promise.reject("ALREADY_SCANNING", "Scan already in progress");
                return;
            }

            Log.d(TAG, "Starting BLE scan with Nordic SDK BluetoothLeScannerCompat...");

            // Usa il Nordic Scanner come nell'SDK ufficiale
            final BluetoothLeScannerCompat scanner = BluetoothLeScannerCompat.getScanner();
            if (scanner == null) {
                Log.e(TAG, "BluetoothLeScannerCompat.getScanner() returned null");
                promise.reject("SCANNER_ERROR", "Nordic scanner not available");
                return;
            }

            Log.d(TAG, "Nordic scanner obtained successfully");

            ScanSettings settings = new ScanSettings.Builder()
                .setScanMode(ScanSettings.SCAN_MODE_LOW_LATENCY)
                .setLegacy(false)  // Importante: come nell'SDK ufficiale
                .build();

            // Usa un filtro vuoto come nell'SDK ufficiale invece di null
            List<ScanFilter> scanFilterList = Collections.singletonList(new ScanFilter.Builder().build());
            Log.d(TAG, "Using Nordic BluetoothLeScannerCompat with empty scan filter list");
            Log.d(TAG, "About to call scanner.startScan()...");
            scanner.startScan(scanFilterList, settings, scanCallback);
            Log.d(TAG, "scanner.startScan() called successfully");
            isScanning = true;
            
            sendEvent("onScanStart", null);
            promise.resolve("Scan started");
            
        } catch (Exception e) {
            Log.e(TAG, "Error starting scan", e);
            promise.reject("SCAN_ERROR", e.getMessage());
        }
    }

    @ReactMethod
    public void stopScan(Promise promise) {
        try {
            if (isScanning) {
                final BluetoothLeScannerCompat scanner = BluetoothLeScannerCompat.getScanner();
                scanner.stopScan(scanCallback);
                isScanning = false;
                Log.d(TAG, "Nordic BLE scan stopped");
            }
            
            sendEvent("onScanStop", null);
            promise.resolve("Scan stopped");
        } catch (Exception e) {
            Log.e(TAG, "Error stopping scan", e);
            promise.reject("STOP_ERROR", e.getMessage());
        }
    }

    private DeviceInfo createDeviceInfo(ScanResult result) {
        try {
            byte[] scanRecord = result.getScanRecord() != null ? result.getScanRecord().getBytes() : null;
            if (scanRecord == null || scanRecord.length == 0 || result.getRssi() == 127) {
                return null;
            }
            
            DeviceInfo deviceInfo = new DeviceInfo();
            deviceInfo.name = result.getScanRecord().getDeviceName();
            deviceInfo.rssi = result.getRssi();
            deviceInfo.mac = result.getDevice().getAddress();
            deviceInfo.scanRecord = bytesToHex(scanRecord);
            deviceInfo.scanResult = result;
            
            return deviceInfo;
        } catch (Exception e) {
            Log.e(TAG, "Error creating DeviceInfo", e);
            return null;
        }
    }

    // Implementazione semplificata di BeaconXInfoParseableImpl
    private BeaconXInfo parseBeaconXInfo(DeviceInfo deviceInfo) {
        try {
            ScanResult result = deviceInfo.scanResult;
            ScanRecord record = result.getScanRecord();
            Map<ParcelUuid, byte[]> serviceData = record.getServiceData();
            
            int battery = -1;
            int lockState = -1;
            int ambientLightState = -1;
            int tamperState = -1;
            boolean isBeaconX = false;
            
            // Check manufacturer data per iBeacon Apple
            byte[] manufacturerBytes = record.getManufacturerSpecificData(0x004C);
            if (manufacturerBytes != null && manufacturerBytes.length == 23) {
                // Verifica che sia un iBeacon (primi 2 byte devono essere 0x02, 0x15)
                if (manufacturerBytes.length >= 2 && manufacturerBytes[0] == 0x02 && manufacturerBytes[1] == 0x15) {
                    isBeaconX = true;
                    Log.d(TAG, "Found iBeacon Apple format - accepting as BeaconX");
                }
            }
            
            // Check service data come nell'SDK
            if (serviceData != null && !serviceData.isEmpty()) {
                for (ParcelUuid parcelUuid : serviceData.keySet()) {
                    String uuidStr = parcelUuid.toString().toLowerCase();
                    byte[] bytes = serviceData.get(parcelUuid);
                    
                    if (bytes == null) continue;
                    
                    if (uuidStr.startsWith("0000feaa")) {
                        // Eddystone - accetta qualsiasi frame type
                        isBeaconX = true;
                        if (bytes.length > 0) {
                            int frameType = bytes[0] & 0xFF;
                            Log.d(TAG, "Found Eddystone frame type: " + frameType + " - accepting as BeaconX");
                            if (frameType == 0x20 && bytes.length >= 14) { // TLM
                                battery = ((bytes[2] & 0xFF) << 8) | (bytes[3] & 0xFF);
                            } else if (frameType == 0x00) { // UID
                                Log.d(TAG, "Eddystone UID frame detected");
                            } else if (frameType == 0x10) { // URL
                                Log.d(TAG, "Eddystone URL frame detected");
                            }
                        }
                        
                    } else if (uuidStr.startsWith("0000feab")) {
                        // BeaconX Pro
                        isBeaconX = true;
                        if (bytes.length >= 15 && (bytes[0] & 0xFF) == 0x40) {
                            battery = ((bytes[3] & 0xFF) << 8) | (bytes[4] & 0xFF);
                            lockState = bytes[5] & 2; // 0 or 2
                            int ambientLightSupport = bytes[5] & 4; // 0 or 4
                            if (ambientLightSupport == 4) {
                                ambientLightState = bytes[6] & 2;
                            }
                            tamperState = bytes[5] & 1;
                            Log.d(TAG, "Found BeaconX Pro INFO frame");
                        }
                        
                    } else if (uuidStr.startsWith("0000feac")) {
                        // BeaconX Pro alternativo
                        isBeaconX = true;
                        if (bytes.length >= 15 && (bytes[0] & 0xFF) == 0x40) {
                            battery = ((bytes[3] & 0xFF) << 8) | (bytes[4] & 0xFF);
                            lockState = bytes[5] & 0xFF;
                            Log.d(TAG, "Found BeaconX Pro INFO alternative frame");
                        }
                        
                    } else if (uuidStr.startsWith("0000eb01")) {
                        // BeaconX iBeacon
                        isBeaconX = true;
                        if (bytes.length >= 8) {
                            battery = ((bytes[0] & 0xFF) << 8) | (bytes[1] & 0xFF);
                        }
                        Log.d(TAG, "Found BeaconX iBeacon frame");
                    }
                }
            }
            
            // Fallback: se il dispositivo ha un nome che suggerisce che sia un beacon, accettalo
            if (!isBeaconX && deviceInfo.name != null) {
                String name = deviceInfo.name.toLowerCase();
                if (name.contains("beacon") || name.contains("moko") || name.contains("bxp")) {
                    isBeaconX = true;
                    Log.d(TAG, "Device accepted based on name: " + deviceInfo.name);
                }
            }

            if (!isBeaconX) {
                Log.d(TAG, "Device rejected - not recognized as BeaconX: " + deviceInfo.mac + " (" + deviceInfo.name + ")");
                return null;
            }
            
            // Crea BeaconXInfo come nell'SDK
            BeaconXInfo beaconXInfo = new BeaconXInfo();
            beaconXInfo.name = deviceInfo.name;
            beaconXInfo.mac = deviceInfo.mac;
            beaconXInfo.rssi = deviceInfo.rssi;
            beaconXInfo.battery = battery < 0 ? -1 : battery;
            beaconXInfo.lockState = lockState < 0 ? -1 : lockState;
            beaconXInfo.ambientLightState = ambientLightState < 0 ? -1 : ambientLightState;
            beaconXInfo.tamperState = tamperState < 0 ? -1 : tamperState;
            beaconXInfo.connectState = result.isConnectable() ? 1 : 0;
            beaconXInfo.scanRecord = deviceInfo.scanRecord;
            
            return beaconXInfo;
            
        } catch (Exception e) {
            Log.e(TAG, "Error parsing BeaconX info", e);
            return null;
        }
    }

    // Classi helper
    private static class DeviceInfo {
        String name;
        int rssi;
        String mac;
        String scanRecord;
        ScanResult scanResult;
    }

    private static class BeaconXInfo {
        String name;
        String mac;
        int rssi;
        int battery;
        int lockState;
        int ambientLightState;
        int tamperState;
        int connectState;
        String scanRecord;
    }

    private String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02X", b));
        }
        return result.toString();
    }
}










