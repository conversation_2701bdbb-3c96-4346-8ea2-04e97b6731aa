{"buildFiles": ["C:\\BLE\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\app for beacons\\BeaconL01\\BLEScannerApp\\node_modules\\react-native-ble-plx\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\app for beacons\\BeaconL01\\BLEScannerApp\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\BLE\\android\\app\\.cxx\\Debug\\5t2fl393\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\BLE\\android\\app\\.cxx\\Debug\\5t2fl393\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"artifactName": "react_codegen_safeareacontext", "abi": "x86_64", "output": "C:\\BLE\\android\\app\\build\\intermediates\\cxx\\Debug\\5t2fl393\\obj\\x86_64\\libreact_codegen_safeareacontext.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\a18451f415b73841e30da63b3d9805af\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\cba9d40536536f0777207a470c313f6b\\transformed\\react-android-0.81.1-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\cba9d40536536f0777207a470c313f6b\\transformed\\react-android-0.81.1-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so"]}, "appmodules::@6890427a1f51a3e7e1df": {"artifactName": "appmodules", "abi": "x86_64", "output": "C:\\BLE\\android\\app\\build\\intermediates\\cxx\\Debug\\5t2fl393\\obj\\x86_64\\libappmodules.so", "runtimeFiles": ["C:\\BLE\\android\\app\\build\\intermediates\\cxx\\Debug\\5t2fl393\\obj\\x86_64\\libreact_codegen_safeareacontext.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\a18451f415b73841e30da63b3d9805af\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\cba9d40536536f0777207a470c313f6b\\transformed\\react-android-0.81.1-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\cba9d40536536f0777207a470c313f6b\\transformed\\react-android-0.81.1-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so"]}, "react_codegen_BlePlx::@0d03ad0a4a63aefc401b": {"artifactName": "react_codegen_BlePlx", "abi": "x86_64", "runtimeFiles": []}}}