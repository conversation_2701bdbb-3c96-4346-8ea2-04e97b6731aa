[{"directory": "C:/BLE/android/app/.cxx/Debug/5t2fl393/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dappmodules_EXPORTS -IC:/BLE/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -IC:/BLE/android/app/build/generated/autolinking/src/main/jni -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\C_\\BLE\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp.o -c C:\\BLE\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp", "file": "C:\\BLE\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp"}, {"directory": "C:/BLE/android/app/.cxx/Debug/5t2fl393/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dappmodules_EXPORTS -IC:/BLE/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -IC:/BLE/android/app/build/generated/autolinking/src/main/jni -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\OnLoad.cpp.o -c C:\\BLE\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp", "file": "C:\\BLE\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp"}, {"directory": "C:/BLE/android/app/.cxx/Debug/5t2fl393/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx\" -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o BlePlx_autolinked_build\\CMakeFiles\\react_codegen_BlePlx.dir\\BlePlx-generated.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\app for beacons\\BeaconL01\\BLEScannerApp\\node_modules\\react-native-ble-plx\\android\\build\\generated\\source\\codegen\\jni\\BlePlx-generated.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\app for beacons\\BeaconL01\\BLEScannerApp\\node_modules\\react-native-ble-plx\\android\\build\\generated\\source\\codegen\\jni\\BlePlx-generated.cpp"}, {"directory": "C:/BLE/android/app/.cxx/Debug/5t2fl393/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx\" -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o BlePlx_autolinked_build\\CMakeFiles\\react_codegen_BlePlx.dir\\react\\renderer\\components\\BlePlx\\BlePlxJSI-generated.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\app for beacons\\BeaconL01\\BLEScannerApp\\node_modules\\react-native-ble-plx\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\BlePlx\\BlePlxJSI-generated.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\app for beacons\\BeaconL01\\BLEScannerApp\\node_modules\\react-native-ble-plx\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\BlePlx\\BlePlxJSI-generated.cpp"}, {"directory": "C:/BLE/android/app/.cxx/Debug/5t2fl393/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx\" -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o BlePlx_autolinked_build\\CMakeFiles\\react_codegen_BlePlx.dir\\react\\renderer\\components\\BlePlx\\ComponentDescriptors.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\app for beacons\\BeaconL01\\BLEScannerApp\\node_modules\\react-native-ble-plx\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\BlePlx\\ComponentDescriptors.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\app for beacons\\BeaconL01\\BLEScannerApp\\node_modules\\react-native-ble-plx\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\BlePlx\\ComponentDescriptors.cpp"}, {"directory": "C:/BLE/android/app/.cxx/Debug/5t2fl393/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx\" -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o BlePlx_autolinked_build\\CMakeFiles\\react_codegen_BlePlx.dir\\react\\renderer\\components\\BlePlx\\EventEmitters.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\app for beacons\\BeaconL01\\BLEScannerApp\\node_modules\\react-native-ble-plx\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\BlePlx\\EventEmitters.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\app for beacons\\BeaconL01\\BLEScannerApp\\node_modules\\react-native-ble-plx\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\BlePlx\\EventEmitters.cpp"}, {"directory": "C:/BLE/android/app/.cxx/Debug/5t2fl393/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx\" -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o BlePlx_autolinked_build\\CMakeFiles\\react_codegen_BlePlx.dir\\react\\renderer\\components\\BlePlx\\Props.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\app for beacons\\BeaconL01\\BLEScannerApp\\node_modules\\react-native-ble-plx\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\BlePlx\\Props.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\app for beacons\\BeaconL01\\BLEScannerApp\\node_modules\\react-native-ble-plx\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\BlePlx\\Props.cpp"}, {"directory": "C:/BLE/android/app/.cxx/Debug/5t2fl393/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx\" -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o BlePlx_autolinked_build\\CMakeFiles\\react_codegen_BlePlx.dir\\react\\renderer\\components\\BlePlx\\ShadowNodes.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\app for beacons\\BeaconL01\\BLEScannerApp\\node_modules\\react-native-ble-plx\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\BlePlx\\ShadowNodes.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\app for beacons\\BeaconL01\\BLEScannerApp\\node_modules\\react-native-ble-plx\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\BlePlx\\ShadowNodes.cpp"}, {"directory": "C:/BLE/android/app/.cxx/Debug/5t2fl393/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/.\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx\" -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o BlePlx_autolinked_build\\CMakeFiles\\react_codegen_BlePlx.dir\\react\\renderer\\components\\BlePlx\\States.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\app for beacons\\BeaconL01\\BLEScannerApp\\node_modules\\react-native-ble-plx\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\BlePlx\\States.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\app for beacons\\BeaconL01\\BLEScannerApp\\node_modules\\react-native-ble-plx\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\BlePlx\\States.cpp"}, {"directory": "C:/BLE/android/app/.cxx/Debug/5t2fl393/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreact_codegen_safeareacontext_EXPORTS -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\f1013b39728ee71b47ca2678cb665f90\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\app for beacons\\BeaconL01\\BLEScannerApp\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\app for beacons\\BeaconL01\\BLEScannerApp\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp"}, {"directory": "C:/BLE/android/app/.cxx/Debug/5t2fl393/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreact_codegen_safeareacontext_EXPORTS -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\f1013b39728ee71b47ca2678cb665f90\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\app for beacons\\BeaconL01\\BLEScannerApp\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\app for beacons\\BeaconL01\\BLEScannerApp\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp"}, {"directory": "C:/BLE/android/app/.cxx/Debug/5t2fl393/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreact_codegen_safeareacontext_EXPORTS -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\9f9a3eea2700cf24533e0966acfc7c38\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\app for beacons\\BeaconL01\\BLEScannerApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\app for beacons\\BeaconL01\\BLEScannerApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp"}, {"directory": "C:/BLE/android/app/.cxx/Debug/5t2fl393/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreact_codegen_safeareacontext_EXPORTS -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\a54f1e1d1c0df09c6ec3495181cdbad5\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\app for beacons\\BeaconL01\\BLEScannerApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\app for beacons\\BeaconL01\\BLEScannerApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp"}, {"directory": "C:/BLE/android/app/.cxx/Debug/5t2fl393/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreact_codegen_safeareacontext_EXPORTS -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\22fe2f104738b6b1b5f1b02b343e0d9a\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\app for beacons\\BeaconL01\\BLEScannerApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\app for beacons\\BeaconL01\\BLEScannerApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp"}, {"directory": "C:/BLE/android/app/.cxx/Debug/5t2fl393/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreact_codegen_safeareacontext_EXPORTS -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\a54f1e1d1c0df09c6ec3495181cdbad5\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\app for beacons\\BeaconL01\\BLEScannerApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\app for beacons\\BeaconL01\\BLEScannerApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp"}, {"directory": "C:/BLE/android/app/.cxx/Debug/5t2fl393/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreact_codegen_safeareacontext_EXPORTS -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\22fe2f104738b6b1b5f1b02b343e0d9a\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\app for beacons\\BeaconL01\\BLEScannerApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\app for beacons\\BeaconL01\\BLEScannerApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp"}, {"directory": "C:/BLE/android/app/.cxx/Debug/5t2fl393/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreact_codegen_safeareacontext_EXPORTS -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\7b83fb19fca5517cdad3035a99dc4dc4\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\app for beacons\\BeaconL01\\BLEScannerApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\app for beacons\\BeaconL01\\BLEScannerApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp"}, {"directory": "C:/BLE/android/app/.cxx/Debug/5t2fl393/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DRN_SERIALIZABLE_STATE -Dreact_codegen_safeareacontext_EXPORTS -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\f1013b39728ee71b47ca2678cb665f90\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp.o -c \"C:\\Users\\<USER>\\Desktop\\app for beacons\\BeaconL01\\BLEScannerApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp\"", "file": "C:\\Users\\<USER>\\Desktop\\app for beacons\\BeaconL01\\BLEScannerApp\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp"}]