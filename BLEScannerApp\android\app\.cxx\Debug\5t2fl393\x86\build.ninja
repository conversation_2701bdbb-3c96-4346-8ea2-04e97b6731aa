# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: appmodules
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.8


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = C$:/BLE/android/app/.cxx/Debug/5t2fl393/x86/
# =============================================================================
# Object build statements for SHARED_LIBRARY target appmodules


#############################################
# Order-only phony target for appmodules

build cmake_object_order_depends_target_appmodules: phony || cmake_object_order_depends_target_react_codegen_BlePlx cmake_object_order_depends_target_react_codegen_safeareacontext

build CMakeFiles/appmodules.dir/C_/BLE/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o: CXX_COMPILER__appmodules_Debug C$:/BLE/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp || cmake_object_order_depends_target_appmodules
  DEFINES = -DRN_SERIALIZABLE_STATE -Dappmodules_EXPORTS
  DEP_FILE = CMakeFiles\appmodules.dir\C_\BLE\android\app\build\generated\autolinking\src\main\jni\autolinking.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\"ReactNative\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/BLE/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -IC:/BLE/android/app/build/generated/autolinking/src/main/jni -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni" -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx" -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\appmodules.dir
  OBJECT_FILE_DIR = CMakeFiles\appmodules.dir\C_\BLE\android\app\build\generated\autolinking\src\main\jni

build CMakeFiles/appmodules.dir/OnLoad.cpp.o: CXX_COMPILER__appmodules_Debug C$:/BLE/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp || cmake_object_order_depends_target_appmodules
  DEFINES = -DRN_SERIALIZABLE_STATE -Dappmodules_EXPORTS
  DEP_FILE = CMakeFiles\appmodules.dir\OnLoad.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\"ReactNative\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -IC:/BLE/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -IC:/BLE/android/app/build/generated/autolinking/src/main/jni -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni" -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx" -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\appmodules.dir
  OBJECT_FILE_DIR = CMakeFiles\appmodules.dir


# =============================================================================
# Link build statements for SHARED_LIBRARY target appmodules


#############################################
# Link the shared library C:\BLE\android\app\build\intermediates\cxx\Debug\5t2fl393\obj\x86\libappmodules.so

build C$:/BLE/android/app/build/intermediates/cxx/Debug/5t2fl393/obj/x86/libappmodules.so: CXX_SHARED_LIBRARY_LINKER__appmodules_Debug BlePlx_autolinked_build/CMakeFiles/react_codegen_BlePlx.dir/BlePlx-generated.cpp.o BlePlx_autolinked_build/CMakeFiles/react_codegen_BlePlx.dir/react/renderer/components/BlePlx/BlePlxJSI-generated.cpp.o BlePlx_autolinked_build/CMakeFiles/react_codegen_BlePlx.dir/react/renderer/components/BlePlx/ComponentDescriptors.cpp.o BlePlx_autolinked_build/CMakeFiles/react_codegen_BlePlx.dir/react/renderer/components/BlePlx/EventEmitters.cpp.o BlePlx_autolinked_build/CMakeFiles/react_codegen_BlePlx.dir/react/renderer/components/BlePlx/Props.cpp.o BlePlx_autolinked_build/CMakeFiles/react_codegen_BlePlx.dir/react/renderer/components/BlePlx/ShadowNodes.cpp.o BlePlx_autolinked_build/CMakeFiles/react_codegen_BlePlx.dir/react/renderer/components/BlePlx/States.cpp.o CMakeFiles/appmodules.dir/C_/BLE/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o CMakeFiles/appmodules.dir/OnLoad.cpp.o | C$:/BLE/android/app/build/intermediates/cxx/Debug/5t2fl393/obj/x86/libreact_codegen_safeareacontext.so C$:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86/libfbjni.so C$:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/libs/android.x86/libjsi.so C$:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/libs/android.x86/libreactnative.so || BlePlx_autolinked_build/react_codegen_BlePlx C$:/BLE/android/app/build/intermediates/cxx/Debug/5t2fl393/obj/x86/libreact_codegen_safeareacontext.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = C:/BLE/android/app/build/intermediates/cxx/Debug/5t2fl393/obj/x86/libreact_codegen_safeareacontext.so  C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86/libfbjni.so  C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/libs/android.x86/libjsi.so  C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/libs/android.x86/libreactnative.so  -latomic -lm
  OBJECT_DIR = CMakeFiles\appmodules.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libappmodules.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = C:\BLE\android\app\build\intermediates\cxx\Debug\5t2fl393\obj\x86\libappmodules.so
  TARGET_PDB = appmodules.so.dbg


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\BLE\android\app\.cxx\Debug\5t2fl393\x86 && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\BLE\android\app\.cxx\Debug\5t2fl393\x86 && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\BLE\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BC:\BLE\android\app\.cxx\Debug\5t2fl393\x86"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/BLE/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_BlePlx


#############################################
# Order-only phony target for react_codegen_BlePlx

build cmake_object_order_depends_target_react_codegen_BlePlx: phony || BlePlx_autolinked_build/CMakeFiles/react_codegen_BlePlx.dir

build BlePlx_autolinked_build/CMakeFiles/react_codegen_BlePlx.dir/BlePlx-generated.cpp.o: CXX_COMPILER__react_codegen_BlePlx_Debug C$:/Users/<USER>/Desktop/app$ for$ beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/BlePlx-generated.cpp || cmake_object_order_depends_target_react_codegen_BlePlx
  DEFINES = -DRN_SERIALIZABLE_STATE
  DEP_FILE = BlePlx_autolinked_build\CMakeFiles\react_codegen_BlePlx.dir\BlePlx-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\"ReactNative\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx" -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/include
  OBJECT_DIR = BlePlx_autolinked_build\CMakeFiles\react_codegen_BlePlx.dir
  OBJECT_FILE_DIR = BlePlx_autolinked_build\CMakeFiles\react_codegen_BlePlx.dir

build BlePlx_autolinked_build/CMakeFiles/react_codegen_BlePlx.dir/react/renderer/components/BlePlx/BlePlxJSI-generated.cpp.o: CXX_COMPILER__react_codegen_BlePlx_Debug C$:/Users/<USER>/Desktop/app$ for$ beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx/BlePlxJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_BlePlx
  DEFINES = -DRN_SERIALIZABLE_STATE
  DEP_FILE = BlePlx_autolinked_build\CMakeFiles\react_codegen_BlePlx.dir\react\renderer\components\BlePlx\BlePlxJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\"ReactNative\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx" -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/include
  OBJECT_DIR = BlePlx_autolinked_build\CMakeFiles\react_codegen_BlePlx.dir
  OBJECT_FILE_DIR = BlePlx_autolinked_build\CMakeFiles\react_codegen_BlePlx.dir\react\renderer\components\BlePlx

build BlePlx_autolinked_build/CMakeFiles/react_codegen_BlePlx.dir/react/renderer/components/BlePlx/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_BlePlx_Debug C$:/Users/<USER>/Desktop/app$ for$ beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_BlePlx
  DEFINES = -DRN_SERIALIZABLE_STATE
  DEP_FILE = BlePlx_autolinked_build\CMakeFiles\react_codegen_BlePlx.dir\react\renderer\components\BlePlx\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\"ReactNative\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx" -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/include
  OBJECT_DIR = BlePlx_autolinked_build\CMakeFiles\react_codegen_BlePlx.dir
  OBJECT_FILE_DIR = BlePlx_autolinked_build\CMakeFiles\react_codegen_BlePlx.dir\react\renderer\components\BlePlx

build BlePlx_autolinked_build/CMakeFiles/react_codegen_BlePlx.dir/react/renderer/components/BlePlx/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_BlePlx_Debug C$:/Users/<USER>/Desktop/app$ for$ beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_BlePlx
  DEFINES = -DRN_SERIALIZABLE_STATE
  DEP_FILE = BlePlx_autolinked_build\CMakeFiles\react_codegen_BlePlx.dir\react\renderer\components\BlePlx\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\"ReactNative\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx" -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/include
  OBJECT_DIR = BlePlx_autolinked_build\CMakeFiles\react_codegen_BlePlx.dir
  OBJECT_FILE_DIR = BlePlx_autolinked_build\CMakeFiles\react_codegen_BlePlx.dir\react\renderer\components\BlePlx

build BlePlx_autolinked_build/CMakeFiles/react_codegen_BlePlx.dir/react/renderer/components/BlePlx/Props.cpp.o: CXX_COMPILER__react_codegen_BlePlx_Debug C$:/Users/<USER>/Desktop/app$ for$ beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx/Props.cpp || cmake_object_order_depends_target_react_codegen_BlePlx
  DEFINES = -DRN_SERIALIZABLE_STATE
  DEP_FILE = BlePlx_autolinked_build\CMakeFiles\react_codegen_BlePlx.dir\react\renderer\components\BlePlx\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\"ReactNative\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx" -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/include
  OBJECT_DIR = BlePlx_autolinked_build\CMakeFiles\react_codegen_BlePlx.dir
  OBJECT_FILE_DIR = BlePlx_autolinked_build\CMakeFiles\react_codegen_BlePlx.dir\react\renderer\components\BlePlx

build BlePlx_autolinked_build/CMakeFiles/react_codegen_BlePlx.dir/react/renderer/components/BlePlx/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_BlePlx_Debug C$:/Users/<USER>/Desktop/app$ for$ beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_BlePlx
  DEFINES = -DRN_SERIALIZABLE_STATE
  DEP_FILE = BlePlx_autolinked_build\CMakeFiles\react_codegen_BlePlx.dir\react\renderer\components\BlePlx\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\"ReactNative\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx" -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/include
  OBJECT_DIR = BlePlx_autolinked_build\CMakeFiles\react_codegen_BlePlx.dir
  OBJECT_FILE_DIR = BlePlx_autolinked_build\CMakeFiles\react_codegen_BlePlx.dir\react\renderer\components\BlePlx

build BlePlx_autolinked_build/CMakeFiles/react_codegen_BlePlx.dir/react/renderer/components/BlePlx/States.cpp.o: CXX_COMPILER__react_codegen_BlePlx_Debug C$:/Users/<USER>/Desktop/app$ for$ beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx/States.cpp || cmake_object_order_depends_target_react_codegen_BlePlx
  DEFINES = -DRN_SERIALIZABLE_STATE
  DEP_FILE = BlePlx_autolinked_build\CMakeFiles\react_codegen_BlePlx.dir\react\renderer\components\BlePlx\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\"ReactNative\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx" -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/include
  OBJECT_DIR = BlePlx_autolinked_build\CMakeFiles\react_codegen_BlePlx.dir
  OBJECT_FILE_DIR = BlePlx_autolinked_build\CMakeFiles\react_codegen_BlePlx.dir\react\renderer\components\BlePlx



#############################################
# Object library react_codegen_BlePlx

build BlePlx_autolinked_build/react_codegen_BlePlx: phony BlePlx_autolinked_build/CMakeFiles/react_codegen_BlePlx.dir/BlePlx-generated.cpp.o BlePlx_autolinked_build/CMakeFiles/react_codegen_BlePlx.dir/react/renderer/components/BlePlx/BlePlxJSI-generated.cpp.o BlePlx_autolinked_build/CMakeFiles/react_codegen_BlePlx.dir/react/renderer/components/BlePlx/ComponentDescriptors.cpp.o BlePlx_autolinked_build/CMakeFiles/react_codegen_BlePlx.dir/react/renderer/components/BlePlx/EventEmitters.cpp.o BlePlx_autolinked_build/CMakeFiles/react_codegen_BlePlx.dir/react/renderer/components/BlePlx/Props.cpp.o BlePlx_autolinked_build/CMakeFiles/react_codegen_BlePlx.dir/react/renderer/components/BlePlx/ShadowNodes.cpp.o BlePlx_autolinked_build/CMakeFiles/react_codegen_BlePlx.dir/react/renderer/components/BlePlx/States.cpp.o


#############################################
# Utility command for edit_cache

build BlePlx_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\BLE\android\app\.cxx\Debug\5t2fl393\x86\BlePlx_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build BlePlx_autolinked_build/edit_cache: phony BlePlx_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build BlePlx_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\BLE\android\app\.cxx\Debug\5t2fl393\x86\BlePlx_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\BLE\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BC:\BLE\android\app\.cxx\Debug\5t2fl393\x86"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build BlePlx_autolinked_build/rebuild_cache: phony BlePlx_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/BLE/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_safeareacontext


#############################################
# Order-only phony target for react_codegen_safeareacontext

build cmake_object_order_depends_target_react_codegen_safeareacontext: phony || safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/f1013b39728ee71b47ca2678cb665f90/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug C$:/Users/<USER>/Desktop/app$ for$ beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\f1013b39728ee71b47ca2678cb665f90\common\cpp\react\renderer\components\safeareacontext\RNCSafeAreaViewShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\"ReactNative\" -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\f1013b39728ee71b47ca2678cb665f90\common\cpp\react\renderer\components\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/f1013b39728ee71b47ca2678cb665f90/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug C$:/Users/<USER>/Desktop/app$ for$ beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\f1013b39728ee71b47ca2678cb665f90\common\cpp\react\renderer\components\safeareacontext\RNCSafeAreaViewState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\"ReactNative\" -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\f1013b39728ee71b47ca2678cb665f90\common\cpp\react\renderer\components\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/a54f1e1d1c0df09c6ec3495181cdbad5/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug C$:/Users/<USER>/Desktop/app$ for$ beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\a54f1e1d1c0df09c6ec3495181cdbad5\source\codegen\jni\react\renderer\components\safeareacontext\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\"ReactNative\" -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\a54f1e1d1c0df09c6ec3495181cdbad5\source\codegen\jni\react\renderer\components\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/22fe2f104738b6b1b5f1b02b343e0d9a/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug C$:/Users/<USER>/Desktop/app$ for$ beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\22fe2f104738b6b1b5f1b02b343e0d9a\generated\source\codegen\jni\react\renderer\components\safeareacontext\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\"ReactNative\" -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\22fe2f104738b6b1b5f1b02b343e0d9a\generated\source\codegen\jni\react\renderer\components\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3b3b825b7a3fa48c4463d6d0f5aa1f15/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug C$:/Users/<USER>/Desktop/app$ for$ beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\3b3b825b7a3fa48c4463d6d0f5aa1f15\build\generated\source\codegen\jni\react\renderer\components\safeareacontext\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\"ReactNative\" -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\3b3b825b7a3fa48c4463d6d0f5aa1f15\build\generated\source\codegen\jni\react\renderer\components\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3b3b825b7a3fa48c4463d6d0f5aa1f15/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug C$:/Users/<USER>/Desktop/app$ for$ beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\3b3b825b7a3fa48c4463d6d0f5aa1f15\build\generated\source\codegen\jni\react\renderer\components\safeareacontext\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\"ReactNative\" -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\3b3b825b7a3fa48c4463d6d0f5aa1f15\build\generated\source\codegen\jni\react\renderer\components\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3b3b825b7a3fa48c4463d6d0f5aa1f15/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug C$:/Users/<USER>/Desktop/app$ for$ beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\3b3b825b7a3fa48c4463d6d0f5aa1f15\build\generated\source\codegen\jni\react\renderer\components\safeareacontext\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\"ReactNative\" -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\3b3b825b7a3fa48c4463d6d0f5aa1f15\build\generated\source\codegen\jni\react\renderer\components\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/9f9a3eea2700cf24533e0966acfc7c38/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug C$:/Users/<USER>/Desktop/app$ for$ beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\9f9a3eea2700cf24533e0966acfc7c38\codegen\jni\react\renderer\components\safeareacontext\safeareacontextJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\"ReactNative\" -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\9f9a3eea2700cf24533e0966acfc7c38\codegen\jni\react\renderer\components\safeareacontext

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/f1013b39728ee71b47ca2678cb665f90/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug C$:/Users/<USER>/Desktop/app$ for$ beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -DRN_SERIALIZABLE_STATE -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\f1013b39728ee71b47ca2678cb665f90\android\build\generated\source\codegen\jni\safeareacontext-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\"ReactNative\" -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\f1013b39728ee71b47ca2678cb665f90\android\build\generated\source\codegen\jni


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_safeareacontext


#############################################
# Link the shared library C:\BLE\android\app\build\intermediates\cxx\Debug\5t2fl393\obj\x86\libreact_codegen_safeareacontext.so

build C$:/BLE/android/app/build/intermediates/cxx/Debug/5t2fl393/obj/x86/libreact_codegen_safeareacontext.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_safeareacontext_Debug safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/f1013b39728ee71b47ca2678cb665f90/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/f1013b39728ee71b47ca2678cb665f90/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/a54f1e1d1c0df09c6ec3495181cdbad5/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/22fe2f104738b6b1b5f1b02b343e0d9a/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3b3b825b7a3fa48c4463d6d0f5aa1f15/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3b3b825b7a3fa48c4463d6d0f5aa1f15/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3b3b825b7a3fa48c4463d6d0f5aa1f15/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/9f9a3eea2700cf24533e0966acfc7c38/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/f1013b39728ee71b47ca2678cb665f90/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o | C$:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86/libfbjni.so C$:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/libs/android.x86/libjsi.so C$:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/libs/android.x86/libreactnative.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86/libfbjni.so  C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/libs/android.x86/libjsi.so  C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/libs/android.x86/libreactnative.so  -latomic -lm
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact_codegen_safeareacontext.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = C:\BLE\android\app\build\intermediates\cxx\Debug\5t2fl393\obj\x86\libreact_codegen_safeareacontext.so
  TARGET_PDB = react_codegen_safeareacontext.so.dbg


#############################################
# Utility command for edit_cache

build safeareacontext_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\BLE\android\app\.cxx\Debug\5t2fl393\x86\safeareacontext_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build safeareacontext_autolinked_build/edit_cache: phony safeareacontext_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build safeareacontext_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\BLE\android\app\.cxx\Debug\5t2fl393\x86\safeareacontext_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SC:\BLE\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BC:\BLE\android\app\.cxx\Debug\5t2fl393\x86"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build safeareacontext_autolinked_build/rebuild_cache: phony safeareacontext_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build appmodules: phony C$:/BLE/android/app/build/intermediates/cxx/Debug/5t2fl393/obj/x86/libappmodules.so

build libappmodules.so: phony C$:/BLE/android/app/build/intermediates/cxx/Debug/5t2fl393/obj/x86/libappmodules.so

build libreact_codegen_safeareacontext.so: phony C$:/BLE/android/app/build/intermediates/cxx/Debug/5t2fl393/obj/x86/libreact_codegen_safeareacontext.so

build react_codegen_BlePlx: phony BlePlx_autolinked_build/react_codegen_BlePlx

build react_codegen_safeareacontext: phony C$:/BLE/android/app/build/intermediates/cxx/Debug/5t2fl393/obj/x86/libreact_codegen_safeareacontext.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: C:/BLE/android/app/.cxx/Debug/5t2fl393/x86

build all: phony C$:/BLE/android/app/build/intermediates/cxx/Debug/5t2fl393/obj/x86/libappmodules.so BlePlx_autolinked_build/all safeareacontext_autolinked_build/all

# =============================================================================

#############################################
# Folder: C:/BLE/android/app/.cxx/Debug/5t2fl393/x86/BlePlx_autolinked_build

build BlePlx_autolinked_build/all: phony BlePlx_autolinked_build/react_codegen_BlePlx

# =============================================================================

#############################################
# Folder: C:/BLE/android/app/.cxx/Debug/5t2fl393/x86/safeareacontext_autolinked_build

build safeareacontext_autolinked_build/all: phony C$:/BLE/android/app/build/intermediates/cxx/Debug/5t2fl393/obj/x86/libreact_codegen_safeareacontext.so

# =============================================================================
# Built-in targets


#############################################
# Phony target to force glob verification run.

build C$:/BLE/android/app/.cxx/Debug/5t2fl393/x86/CMakeFiles/VerifyGlobs.cmake_force: phony


#############################################
# Re-run CMake to check if globbed directories changed.

build C$:/BLE/android/app/.cxx/Debug/5t2fl393/x86/CMakeFiles/cmake.verify_globs: VERIFY_GLOBS | C$:/BLE/android/app/.cxx/Debug/5t2fl393/x86/CMakeFiles/VerifyGlobs.cmake_force
  pool = console
  restat = 1


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE C$:/BLE/android/app/.cxx/Debug/5t2fl393/x86/CMakeFiles/cmake.verify_globs | C$:/BLE/android/app/.cxx/Debug/5t2fl393/x86/CMakeFiles/VerifyGlobs.cmake C$:/BLE/android/app/.cxx/Debug/5t2fl393/prefab/x86/prefab/lib/i686-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake C$:/BLE/android/app/.cxx/Debug/5t2fl393/prefab/x86/prefab/lib/i686-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake C$:/BLE/android/app/.cxx/Debug/5t2fl393/prefab/x86/prefab/lib/i686-linux-android/cmake/fbjni/fbjniConfig.cmake C$:/BLE/android/app/.cxx/Debug/5t2fl393/prefab/x86/prefab/lib/i686-linux-android/cmake/fbjni/fbjniConfigVersion.cmake C$:/BLE/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake C$:/BLE/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake C$:/BLE/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt C$:/BLE/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake C$:/BLE/node_modules/react-native/ReactCommon/cmake-utils/react-native-flags.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CheckIPOSupported.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CheckIPOSupported/CMakeLists-CXX.txt.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CheckIPOSupported/foo.cpp C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CheckIPOSupported/main.cpp C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/abis.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/flags.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/platforms.cmake C$:/Users/<USER>/Desktop/app$ for$ beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/Desktop/app$ for$ beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:/BLE/android/app/.cxx/Debug/5t2fl393/x86/CMakeFiles/VerifyGlobs.cmake C$:/BLE/android/app/.cxx/Debug/5t2fl393/prefab/x86/prefab/lib/i686-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake C$:/BLE/android/app/.cxx/Debug/5t2fl393/prefab/x86/prefab/lib/i686-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake C$:/BLE/android/app/.cxx/Debug/5t2fl393/prefab/x86/prefab/lib/i686-linux-android/cmake/fbjni/fbjniConfig.cmake C$:/BLE/android/app/.cxx/Debug/5t2fl393/prefab/x86/prefab/lib/i686-linux-android/cmake/fbjni/fbjniConfigVersion.cmake C$:/BLE/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake C$:/BLE/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake C$:/BLE/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt C$:/BLE/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake C$:/BLE/node_modules/react-native/ReactCommon/cmake-utils/react-native-flags.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CheckIPOSupported.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CheckIPOSupported/CMakeLists-CXX.txt.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CheckIPOSupported/foo.cpp C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CheckIPOSupported/main.cpp C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/abis.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/flags.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/platforms.cmake C$:/Users/<USER>/Desktop/app$ for$ beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/Desktop/app$ for$ beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
