{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "BlePlx_autolinked_build", "jsonFile": "directory-BlePlx_autolinked_build-Debug-d29fd34fc33c17ea48fe.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni", "targetIndexes": [1]}, {"build": "safeareacontext_autolinked_build", "jsonFile": "directory-safeareacontext_autolinked_build-Debug-a36472163a531e398ee1.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni", "targetIndexes": [2]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1, 2], "name": "appmodules", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-Debug-0a9aba919a0ea5d0ddb1.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 1, "id": "react_codegen_BlePlx::@0d03ad0a4a63aefc401b", "jsonFile": "target-react_codegen_BlePlx-Debug-aa066df32fc1a4b5c2fd.json", "name": "react_codegen_BlePlx", "projectIndex": 0}, {"directoryIndex": 2, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a", "jsonFile": "target-react_codegen_safeareacontext-Debug-ab11a0c6702060a1a7c5.json", "name": "react_codegen_safeareacontext", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/BLE/android/app/.cxx/Debug/5t2fl393/x86_64", "source": "C:/BLE/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}