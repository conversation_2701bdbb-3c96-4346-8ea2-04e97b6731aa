-- Merging decision tree log ---
manifest
ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:1:1-36:12
INJECTED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:1:1-36:12
	package
		ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:3:5-38
		INJECTED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:2:5-51
	xmlns:android
		ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.MOUNT_UNMOUNT_FILESYSTEMS
ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:5:5-7:47
	tools:ignore
		ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:7:9-44
	android:name
		ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:6:9-68
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:9:5-11:38
	android:maxSdkVersion
		ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:11:9-35
	android:name
		ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:10:9-65
uses-permission#android.permission.BLUETOOTH
ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:12:5-14:38
	android:maxSdkVersion
		ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:14:9-35
	android:name
		ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:13:9-52
uses-permission#android.permission.BLUETOOTH_ADMIN
ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:15:5-17:38
	android:maxSdkVersion
		ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:17:9-35
	android:name
		ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:16:9-58
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:19:5-80
	android:name
		ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:19:22-78
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:20:5-79
	android:name
		ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:20:22-76
uses-permission#android.permission.BLUETOOTH_SCAN
ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:23:5-73
	android:name
		ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:23:22-70
uses-permission#android.permission.BLUETOOTH_CONNECT
ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:25:5-76
	android:name
		ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:25:22-73
uses-feature#android.hardware.bluetooth_le
ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:27:5-29:35
	android:required
		ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:29:9-32
	android:name
		ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:28:9-53
application
ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:30:5-34:19
	android:supportsRtl
		ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:32:9-35
	android:allowBackup
		ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:31:9-35
uses-sdk
INJECTED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml
INJECTED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml
