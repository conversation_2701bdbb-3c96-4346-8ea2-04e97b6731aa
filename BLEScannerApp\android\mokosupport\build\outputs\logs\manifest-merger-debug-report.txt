-- Merging decision tree log ---
manifest
ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:1:1-14:12
INJECTED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:1:1-14:12
	package
		INJECTED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.BLUETOOTH
ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:3:5-68
	android:name
		ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:3:22-65
uses-permission#android.permission.BLUETOOTH_ADMIN
ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:4:5-74
	android:name
		ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:4:22-71
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:5:5-81
	android:name
		ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:5:22-78
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:6:5-79
	android:name
		ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:6:22-76
uses-permission#android.permission.BLUETOOTH_SCAN
ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:7:5-73
	android:name
		ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:7:22-70
uses-permission#android.permission.BLUETOOTH_CONNECT
ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:8:5-76
	android:name
		ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:8:22-73
uses-feature#android.hardware.bluetooth_le
ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:10:5-12:35
	android:required
		ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:12:9-32
	android:name
		ADDED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:11:9-53
uses-sdk
INJECTED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml
INJECTED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\BLE\android\mokosupport\src\main\AndroidManifest.xml
