apply plugin: 'com.android.library'

android {
    namespace "com.moko.support.nordic"
    compileSdk 35

    defaultConfig {
        minSdk 24
        targetSdk 35
        versionCode 4
        versionName "4.0"
    }
    buildTypes {
        release {
            minifyEnabled false
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    lintOptions {
        abortOnError false
        disable 'SetTextI18n'
        checkReleaseBuilds false
    }
}

dependencies {
    api 'com.github.MOKO-Android-Base-Library:MKBleLib:1.0.0-beacon'
    implementation 'androidx.appcompat:appcompat:1.6.1'
}

