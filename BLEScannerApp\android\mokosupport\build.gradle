apply plugin: 'com.android.library'

android {
    namespace "com.moko.support.nordic"
    compileSdk 35

    defaultConfig {
        minSdk 24
        targetSdk 35
        versionCode 4
        versionName "4.0"
    }
    buildTypes {
        release {
            minifyEnabled false
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    lintOptions {
        abortOnError false
        disable 'SetTextI18n'
        checkReleaseBuilds false
    }
}

dependencies {
    api 'com.github.MOKO-Android-Base-Library:MKBleLib:1.0.0-beacon'
    implementation 'androidx.appcompat:appcompat:1.6.1'
}

//修改jar名字+将指定jar生成的地方
task makeJar(type: Copy) {
    //如果之前存在，则先删除
    delete 'build/libs/mokoSupport.jar'
    //设置拷贝的文件
    from('build/intermediates/aar_main_jar/debug/')
    //生成jar包后的文件目录位置
    into('build/libs/')
    //include,exclude参数来设置过滤
    include('classes.jar')
    //重命名
    rename('classes.jar', 'mokoSupport.jar')
}

makeJar.dependsOn(build)
