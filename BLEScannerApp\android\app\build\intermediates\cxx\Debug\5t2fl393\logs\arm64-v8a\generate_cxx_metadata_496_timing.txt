# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 655ms
  generate-prefab-packages
    [gap of 154ms]
    exec-prefab 2850ms
    [gap of 69ms]
  generate-prefab-packages completed in 3073ms
  execute-generate-process
    [gap of 22ms]
    exec-configure 3075ms
    [gap of 272ms]
  execute-generate-process completed in 3369ms
  [gap of 40ms]
  remove-unexpected-so-files 15ms
  [gap of 51ms]
  write-metadata-json-to-file 11ms
generate_cxx_metadata completed in 7252ms

