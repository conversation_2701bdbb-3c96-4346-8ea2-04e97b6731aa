{"logs": [{"outputFile": "com.blescannerapp-mergeDebugResources-2:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\cba9d40536536f0777207a470c313f6b\\transformed\\react-android-0.81.1-debug\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,193,260,326,401,466,531,600,671,744,819,886,956,1029,1101,1178,1254,1326,1396,1465,1545,1613,1683,1750", "endColumns": "65,71,66,65,74,64,64,68,70,72,74,66,69,72,71,76,75,71,69,68,79,67,69,66,68", "endOffsets": "116,188,255,321,396,461,526,595,666,739,814,881,951,1024,1096,1173,1249,1321,1391,1460,1540,1608,1678,1745,1814"}, "to": {"startLines": "29,37,38,39,40,41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2666,3393,3465,3532,3598,3673,3738,3803,3872,3943,4016,4091,4158,4307,4380,4452,4529,4605,4677,4747,4816,4997,5065,5135,5202", "endColumns": "65,71,66,65,74,64,64,68,70,72,74,66,69,72,71,76,75,71,69,68,79,67,69,66,68", "endOffsets": "2727,3460,3527,3593,3668,3733,3798,3867,3938,4011,4086,4153,4223,4375,4447,4524,4600,4672,4742,4811,4891,5060,5130,5197,5266"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\c0bb43adce9f553621f0b37557791d2d\\transformed\\core-1.13.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "30,31,32,33,34,35,36,58", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2732,2824,2923,3017,3111,3204,3297,4896", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "2819,2918,3012,3106,3199,3292,3388,4992"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\9a3d7109ece86782fa80f9daad928ce1\\transformed\\appcompat-1.7.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1109,1205,1300,1394,1490,1582,1674,1766,1844,1940,2035,2130,2227,2323,2421,2572,2666", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1104,1200,1295,1389,1485,1577,1669,1761,1839,1935,2030,2125,2222,2318,2416,2567,2661,2740"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,49", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1109,1205,1300,1394,1490,1582,1674,1766,1844,1940,2035,2130,2227,2323,2421,2572,4228", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1104,1200,1295,1389,1485,1577,1669,1761,1839,1935,2030,2125,2222,2318,2416,2567,2661,4302"}}]}]}