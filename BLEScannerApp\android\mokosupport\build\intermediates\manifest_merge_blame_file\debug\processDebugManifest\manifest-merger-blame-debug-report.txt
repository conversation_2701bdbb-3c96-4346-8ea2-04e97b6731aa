1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    xmlns:tools="http://schemas.android.com/tools"
4    package="com.moko.support.nordic" >
5
6    <uses-sdk android:minSdkVersion="24" />
7    <!-- SDCard中创建与删除文件权限 -->
8    <uses-permission
8-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:5:5-7:47
9        android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"
9-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:6:9-68
10        tools:ignore="ProtectedPermissions" />
10-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:7:9-44
11    <!-- 向SDCard写入数据权限 -->
12    <uses-permission
12-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:9:5-11:38
13        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
13-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:10:9-65
14        android:maxSdkVersion="28" />
14-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:11:9-35
15    <uses-permission
15-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:12:5-14:38
16        android:name="android.permission.BLUETOOTH"
16-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:13:9-52
17        android:maxSdkVersion="30" />
17-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:14:9-35
18    <uses-permission
18-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:15:5-17:38
19        android:name="android.permission.BLUETOOTH_ADMIN"
19-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:16:9-58
20        android:maxSdkVersion="30" />
20-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:17:9-35
21    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
21-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:19:5-80
21-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:19:22-78
22    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
22-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:20:5-79
22-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:20:22-76
23
24    <!-- 12新权限 -->
25    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
25-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:23:5-73
25-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:23:22-70
26    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
26-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:25:5-76
26-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:25:22-73
27    <!-- 按时required为true时，则应用只能在支持BLE的Android设备上安装运行；required为false时，Android设备均可正常安装运行 -->
28    <uses-feature
28-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:27:5-29:35
29        android:name="android.hardware.bluetooth_le"
29-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:28:9-53
30        android:required="true" />
30-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:29:9-32
31
32    <application
32-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:30:5-34:19
33        android:allowBackup="true"
33-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:31:9-35
34        android:supportsRtl="true" >
34-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:32:9-35
35    </application>
36
37</manifest>
