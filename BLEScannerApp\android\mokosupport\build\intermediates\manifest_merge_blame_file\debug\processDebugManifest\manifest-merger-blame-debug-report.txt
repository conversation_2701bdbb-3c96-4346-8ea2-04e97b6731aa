1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.moko.support.nordic" >
4
5    <uses-sdk android:minSdkVersion="24" />
6
7    <uses-permission android:name="android.permission.BLUETOOTH" />
7-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:3:5-68
7-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:3:22-65
8    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
8-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:4:5-74
8-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:4:22-71
9    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
9-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:5:5-81
9-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:5:22-78
10    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
10-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:6:5-79
10-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:6:22-76
11    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
11-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:7:5-73
11-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:7:22-70
12    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
12-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:8:5-76
12-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:8:22-73
13
14    <uses-feature
14-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:10:5-12:35
15        android:name="android.hardware.bluetooth_le"
15-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:11:9-53
16        android:required="true" />
16-->C:\BLE\android\mokosupport\src\main\AndroidManifest.xml:12:9-32
17
18</manifest>
