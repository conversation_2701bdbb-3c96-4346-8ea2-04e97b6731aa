{"logs": [{"outputFile": "com.blescannerapp-mergeDebugResources-2:/values/values.xml", "map": [{"source": "C:\\BLE\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "54", "endLines": "6", "endColumns": "12", "endOffsets": "267"}, "to": {"startLines": "433", "startColumns": "4", "startOffsets": "31859", "endLines": "436", "endColumns": "12", "endOffsets": "32037"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\b2ca67d787aef4b1f7f6ac4349cb330e\\transformed\\fragment-1.5.4\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "251,265,294,2878,2883", "startColumns": "4,4,4,4,4", "startOffsets": "16444,17050,18536,169030,169200", "endLines": "251,265,294,2882,2886", "endColumns": "56,64,63,24,24", "endOffsets": "16496,17110,18595,169195,169344"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\cba9d40536536f0777207a470c313f6b\\transformed\\react-android-0.81.1-debug\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,92,96,100,103,107,111,115,118,121,122,123,132,139,146,149,152,155,161,164", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,181,232,286,345,393,442,491,540,596,644,693,751,800,836,886,927,971,1022,1066,1109,1143,1182,1228,1276,1318,1372,1484,1601,1724,1844,1958,2084,2239,2624,2720,2840,2960,3062,3202,3324,3434,3541,3644,3755,3924,4092,4209,4328,4441,4627,4735,4848,4939,5050,5219,5317,5442,5537,5644,5814,5912,6095,6268,6380,6481,6640,6774,6914,7102,7207,7338,7507,7624,7772,7917,8067,8166,8262,8458,8641,8740,8924,9091,9339,9587,9830,9990,10192,10398,10595,10771,10935,10961,10996,11534,11952,12330,12507,12686,12869,13234,13431", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82,83,84,85,86,87,91,95,99,102,106,110,114,117,120,121,122,131,138,145,148,151,154,160,163,173", "endColumns": "62,62,50,53,58,47,48,48,48,55,47,48,57,48,35,49,40,43,50,43,42,33,38,45,47,41,53,47,116,122,119,113,125,154,384,95,119,119,101,139,121,109,106,102,110,168,167,116,118,112,185,107,112,90,110,168,97,124,94,106,169,97,182,172,111,100,158,133,139,101,104,130,168,116,147,144,149,98,95,195,182,98,183,166,10,10,12,12,10,10,10,12,12,25,34,10,10,10,10,10,12,12,12,10", "endOffsets": "113,176,227,281,340,388,437,486,535,591,639,688,746,795,831,881,922,966,1017,1061,1104,1138,1177,1223,1271,1313,1367,1415,1596,1719,1839,1953,2079,2234,2619,2715,2835,2955,3057,3197,3319,3429,3536,3639,3750,3919,4087,4204,4323,4436,4622,4730,4843,4934,5045,5214,5312,5437,5532,5639,5809,5907,6090,6263,6375,6476,6635,6769,6909,7011,7202,7333,7502,7619,7767,7912,8062,8161,8257,8453,8636,8735,8919,9086,9334,9582,9825,9985,10187,10393,10590,10766,10930,10956,10991,11529,11947,12325,12502,12681,12864,13229,13426,13867"}, "to": {"startLines": "32,33,202,203,204,237,238,239,240,241,242,243,244,245,250,253,254,257,258,259,262,264,283,284,286,287,288,289,328,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,394,395,396,397,398,399,400,401,402,404,405,407,408,409,410,411,412,413,414,416,417,418,419,425,429,1520,1524,1527,1531,1535,1754,1757,1833,1880,1881,1890,1897,1904,1907,1910,1913,1919,2065", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2224,2287,13674,13725,13779,15748,15796,15845,15894,15943,15999,16047,16096,16154,16408,16535,16585,16696,16740,16791,16931,17016,18020,18059,18137,18185,18227,18281,20843,21605,21728,21848,21962,22088,22243,22628,22724,22844,22964,23066,23206,23328,23438,23545,23648,23759,23928,24096,24213,24332,24445,24631,24739,24852,24943,25054,25223,25321,25446,25541,25648,27517,27615,27798,27971,28083,28184,28343,28477,28617,28808,28913,29097,29266,29383,29531,29676,29826,29925,30021,30288,30471,30570,30754,31363,31611,101040,101283,101443,101645,101851,117220,117396,124460,127505,127540,128078,128496,128874,129051,129230,129413,129778,140659", "endLines": "32,33,202,203,204,237,238,239,240,241,242,243,244,245,250,253,254,257,258,259,262,264,283,284,286,287,288,289,328,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,394,395,396,397,398,399,400,401,402,404,405,407,408,409,410,411,412,413,414,416,417,418,419,428,432,1523,1526,1530,1534,1538,1756,1759,1833,1880,1889,1896,1903,1906,1909,1912,1918,1921,2074", "endColumns": "62,62,50,53,58,47,48,48,48,55,47,48,57,48,35,49,40,43,50,43,42,33,38,45,47,41,53,47,116,122,119,113,125,154,384,95,119,119,101,139,121,109,106,102,110,168,167,116,118,112,185,107,112,90,110,168,97,124,94,106,169,97,182,172,111,100,158,133,139,101,104,130,168,116,147,144,149,98,95,195,182,98,183,166,10,10,12,12,10,10,10,12,12,25,34,10,10,10,10,10,12,12,12,10", "endOffsets": "2282,2345,13720,13774,13833,15791,15840,15889,15938,15994,16042,16091,16149,16198,16439,16580,16621,16735,16786,16830,16969,17045,18054,18100,18180,18222,18276,18324,20955,21723,21843,21957,22083,22238,22623,22719,22839,22959,23061,23201,23323,23433,23540,23643,23754,23923,24091,24208,24327,24440,24626,24734,24847,24938,25049,25218,25316,25441,25536,25643,25813,27610,27793,27966,28078,28179,28338,28472,28612,28714,28908,29039,29261,29378,29526,29671,29821,29920,30016,30212,30466,30565,30749,30916,31606,31854,101278,101438,101640,101846,102043,117391,117555,124481,127535,128073,128491,128869,129046,129225,129408,129773,129970,141095"}}, {"source": "C:\\BLE\\android\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "50", "endOffsets": "62"}, "to": {"startLines": "330", "startColumns": "4", "startOffsets": "21043", "endColumns": "50", "endOffsets": "21089"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\5b13d35170a3aa7a0bc13eee726c5992\\transformed\\MKBleLib-1.0.0-beacon\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,106,161,250,308,362,455,517,589,657,737,796,865,931,993,1071,1148,1232,1292,1387,1472,1566,1628,1694", "endColumns": "50,54,88,57,53,92,61,71,67,79,58,68,65,61,77,76,83,59,94,84,93,61,65,59", "endOffsets": "101,156,245,303,357,450,512,584,652,732,791,860,926,988,1066,1143,1227,1287,1382,1467,1561,1623,1689,1749"}, "to": {"startLines": "370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "25818,25869,25924,26013,26071,26125,26218,26280,26352,26420,26500,26559,26628,26694,26756,26834,26911,26995,27055,27150,27235,27329,27391,27457", "endColumns": "50,54,88,57,53,92,61,71,67,79,58,68,65,61,77,76,83,59,94,84,93,61,65,59", "endOffsets": "25864,25919,26008,26066,26120,26213,26275,26347,26415,26495,26554,26623,26689,26751,26829,26906,26990,27050,27145,27230,27324,27386,27452,27512"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\c0bb43adce9f553621f0b37557791d2d\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "5,16,17,30,31,56,57,160,161,162,163,164,165,166,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,199,200,201,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,255,256,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,300,331,332,333,334,335,336,337,415,1816,1817,1821,1822,1826,2063,2064,2734,2768,2824,2857,3021,3054", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "254,973,1045,2093,2158,3703,3772,10758,10828,10896,10968,11038,11099,11173,12030,12091,12152,12214,12278,12340,12401,12469,12569,12629,12695,12768,12837,12894,12946,13461,13533,13609,13838,13897,13956,14016,14076,14136,14196,14256,14316,14376,14436,14496,14556,14615,14675,14735,14795,14855,14915,14975,15035,15095,15155,15215,15274,15334,15394,15453,15512,15571,15630,15689,16626,16661,17161,17216,17279,17334,17392,17450,17511,17574,17631,17682,17732,17793,17850,17916,17950,17985,18907,21094,21161,21233,21302,21371,21445,21517,30217,123274,123391,123592,123702,123903,140520,140592,162815,164388,166618,168349,173200,173882", "endLines": "5,16,17,30,31,56,57,160,161,162,163,164,165,166,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,199,200,201,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,255,256,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,300,331,332,333,334,335,336,337,415,1816,1820,1821,1825,1826,2063,2064,2739,2777,2856,2877,3053,3059", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "309,1040,1128,2153,2219,3767,3830,10823,10891,10963,11033,11094,11168,11241,12086,12147,12209,12273,12335,12396,12464,12564,12624,12690,12763,12832,12889,12941,13003,13528,13604,13669,13892,13951,14011,14071,14131,14191,14251,14311,14371,14431,14491,14551,14610,14670,14730,14790,14850,14910,14970,15030,15090,15150,15210,15269,15329,15389,15448,15507,15566,15625,15684,15743,16656,16691,17211,17274,17329,17387,17445,17506,17569,17626,17677,17727,17788,17845,17911,17945,17980,18015,18972,21156,21228,21297,21366,21440,21512,21600,30283,123386,123587,123697,123898,124027,140587,140654,163013,164684,168344,169025,173877,174044"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\1ca3663a1fa1b38f8b90bac9094d5e60\\transformed\\autofill-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,19,20,27,32,37,44,53", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,934,994,1376,1656,1938,2322,2820", "endLines": "2,18,19,26,31,36,43,52,66", "endColumns": "67,12,59,12,12,12,12,12,24", "endOffsets": "118,929,989,1371,1651,1933,2317,2815,3867"}, "to": {"startLines": "159,1864,2029,2030,2037,2042,2047,2054,2716", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10690,126694,138634,138694,139076,139356,139638,140022,162094", "endLines": "159,1879,2029,2036,2041,2046,2053,2062,2729", "endColumns": "67,12,59,12,12,12,12,12,24", "endOffsets": "10753,127500,138689,139071,139351,139633,140017,140515,162675"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f2c15e360ba8954837caf13e2707c423\\transformed\\swiperefreshlayout-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "24", "endOffsets": "287"}, "to": {"startLines": "3427", "startColumns": "4", "startOffsets": "184498", "endLines": "3430", "endColumns": "24", "endOffsets": "184664"}}, {"source": "C:\\BLE\\android\\app\\build\\generated\\res\\resValues\\debug\\values\\gradleResValues.xml", "from": {"startLines": "6,8", "startColumns": "4,4", "startOffsets": "159,265", "endColumns": "63,88", "endOffsets": "218,349"}, "to": {"startLines": "299,403", "startColumns": "4,4", "startOffsets": "18843,28719", "endColumns": "63,88", "endOffsets": "18902,28803"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\9a3d7109ece86782fa80f9daad928ce1\\transformed\\appcompat-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "2,3,4,6,7,8,9,10,11,12,13,14,15,18,19,20,21,22,23,24,25,26,27,28,29,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,167,168,169,170,171,172,173,174,175,191,192,193,194,195,196,197,198,246,247,248,249,252,260,261,266,285,295,296,297,298,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,406,420,421,422,423,424,437,445,446,450,454,458,463,469,476,480,484,489,493,497,501,505,509,513,519,523,529,533,539,543,548,552,555,559,565,569,575,579,585,588,592,596,600,604,608,609,610,611,614,617,620,623,627,628,629,630,631,634,636,638,640,645,646,650,656,660,661,663,675,676,680,686,690,691,692,696,723,727,728,732,760,932,958,1129,1155,1186,1194,1200,1216,1238,1243,1248,1258,1267,1276,1280,1287,1306,1313,1314,1323,1326,1329,1333,1337,1341,1344,1345,1350,1355,1365,1370,1377,1383,1384,1387,1391,1396,1398,1400,1403,1406,1408,1412,1415,1422,1425,1428,1432,1434,1438,1440,1442,1444,1448,1456,1464,1476,1482,1491,1494,1505,1508,1509,1514,1515,1539,1608,1678,1679,1689,1698,1699,1701,1705,1708,1711,1714,1717,1720,1723,1726,1730,1733,1736,1739,1743,1746,1750,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1780,1782,1784,1785,1786,1787,1788,1789,1790,1791,1793,1794,1796,1797,1799,1801,1802,1804,1805,1806,1807,1808,1809,1811,1812,1813,1814,1815,1827,1829,1831,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1848,1849,1850,1851,1852,1853,1854,1856,1860,1922,1923,1924,1925,1926,1927,1931,1932,1933,1934,1936,1938,1940,1942,1944,1945,1946,1947,1949,1951,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1967,1968,1969,1970,1972,1974,1975,1977,1978,1980,1982,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1997,1998,1999,2000,2002,2003,2004,2005,2006,2008,2010,2012,2014,2015,2016,2017,2018,2019,2020,2021,2022,2023,2024,2025,2026,2027,2028,2075,2150,2153,2156,2159,2173,2179,2221,2224,2253,2280,2289,2353,2730,2740,2778,2806,3060,3084,3090,3096,3117,3241,3261,3267,3271,3277,3395,3431,3497,3517,3572,3584,3610", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,314,355,410,472,536,606,667,742,818,895,1133,1218,1300,1376,1452,1529,1607,1713,1819,1898,1978,2035,2350,2424,2499,2564,2630,2690,2751,2823,2896,2963,3031,3090,3149,3208,3267,3326,3380,3434,3487,3541,3595,3649,3835,3909,3988,4061,4135,4206,4278,4350,4423,4480,4538,4611,4685,4759,4834,4906,4979,5049,5120,5180,5241,5310,5379,5449,5523,5599,5663,5740,5816,5893,5958,6027,6104,6179,6248,6316,6393,6459,6520,6617,6682,6751,6850,6921,6980,7038,7095,7154,7218,7289,7361,7433,7505,7577,7644,7712,7780,7839,7902,7966,8056,8147,8207,8273,8340,8406,8476,8540,8593,8660,8721,8788,8901,8959,9022,9087,9152,9227,9300,9372,9416,9463,9509,9558,9619,9680,9741,9803,9867,9931,9995,10060,10123,10183,10244,10310,10369,10429,10491,10562,10622,11246,11332,11419,11509,11596,11684,11766,11849,11939,13008,13060,13118,13163,13229,13293,13350,13407,16203,16260,16308,16357,16501,16835,16882,17115,18105,18600,18664,18726,18786,18977,19051,19121,19199,19253,19323,19408,19456,19502,19563,19626,19692,19756,19827,19890,19955,20019,20080,20141,20193,20266,20340,20409,20484,20558,20632,20773,29044,30921,30999,31089,31177,31273,32042,32624,32713,32960,33241,33493,33778,34171,34648,34870,35092,35368,35595,35825,36055,36285,36515,36742,37161,37387,37812,38042,38470,38689,38972,39180,39311,39538,39964,40189,40616,40837,41262,41382,41658,41959,42283,42574,42888,43025,43156,43261,43503,43670,43874,44082,44353,44465,44577,44682,44799,45013,45159,45299,45385,45733,45821,46067,46485,46734,46816,46914,47571,47671,47923,48347,48602,48696,48785,49022,51046,51288,51390,51643,53799,64480,65996,76691,78219,79976,80602,81022,82283,83548,83804,84040,84587,85081,85686,85884,86464,87832,88207,88325,88863,89020,89216,89489,89745,89915,90056,90120,90485,90852,91528,91792,92130,92483,92577,92763,93069,93331,93456,93583,93822,94033,94152,94345,94522,94977,95158,95280,95539,95652,95839,95941,96048,96177,96452,96960,97456,98333,98627,99197,99346,100078,100250,100334,100670,100762,102048,107279,112650,112712,113290,113874,113965,114078,114307,114467,114619,114790,114956,115125,115292,115455,115698,115868,116041,116212,116486,116685,116890,117560,117644,117740,117836,117934,118034,118136,118238,118340,118442,118544,118644,118740,118852,118981,119104,119235,119366,119464,119578,119672,119812,119946,120042,120154,120254,120370,120466,120578,120678,120818,120954,121118,121248,121406,121556,121697,121841,121976,122088,122238,122366,122494,122630,122762,122892,123022,123134,124032,124178,124322,124486,124552,124642,124718,124822,124912,125014,125122,125230,125330,125410,125502,125600,125710,125762,125840,125946,126038,126142,126252,126374,126537,129975,130055,130155,130245,130355,130445,130686,130780,130886,130978,131078,131190,131304,131420,131536,131630,131744,131856,131958,132078,132200,132282,132386,132506,132632,132730,132824,132912,133024,133140,133262,133374,133549,133665,133751,133843,133955,134079,134146,134272,134340,134468,134612,134740,134809,134904,135019,135132,135231,135340,135451,135562,135663,135768,135868,135998,136089,136212,136306,136418,136504,136608,136704,136792,136910,137014,137118,137244,137332,137440,137540,137630,137740,137824,137926,138010,138064,138128,138234,138320,138430,138514,141100,143716,143834,143949,144029,144390,144623,146027,146105,147449,148810,149198,152041,162680,163018,164689,166046,174049,174800,175062,175262,175641,179919,180525,180754,180905,181120,183648,184669,187695,188439,190570,190910,192221", "endLines": "2,3,4,6,7,8,9,10,11,12,13,14,15,18,19,20,21,22,23,24,25,26,27,28,29,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,167,168,169,170,171,172,173,174,175,191,192,193,194,195,196,197,198,246,247,248,249,252,260,261,266,285,295,296,297,298,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,406,420,421,422,423,424,444,445,449,453,457,462,468,475,479,483,488,492,496,500,504,508,512,518,522,528,532,538,542,547,551,554,558,564,568,574,578,584,587,591,595,599,603,607,608,609,610,613,616,619,622,626,627,628,629,630,633,635,637,639,644,645,649,655,659,660,662,674,675,679,685,689,690,691,695,722,726,727,731,759,931,957,1128,1154,1185,1193,1199,1215,1237,1242,1247,1257,1266,1275,1279,1286,1305,1312,1313,1322,1325,1328,1332,1336,1340,1343,1344,1349,1354,1364,1369,1376,1382,1383,1386,1390,1395,1397,1399,1402,1405,1407,1411,1414,1421,1424,1427,1431,1433,1437,1439,1441,1443,1447,1455,1463,1475,1481,1490,1493,1504,1507,1508,1513,1514,1519,1607,1677,1678,1688,1697,1698,1700,1704,1707,1710,1713,1716,1719,1722,1725,1729,1732,1735,1738,1742,1745,1749,1753,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1781,1783,1784,1785,1786,1787,1788,1789,1790,1792,1793,1795,1796,1798,1800,1801,1803,1804,1805,1806,1807,1808,1810,1811,1812,1813,1814,1815,1828,1830,1832,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1847,1848,1849,1850,1851,1852,1853,1855,1859,1863,1922,1923,1924,1925,1926,1930,1931,1932,1933,1935,1937,1939,1941,1943,1944,1945,1946,1948,1950,1952,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1966,1967,1968,1969,1971,1973,1974,1976,1977,1979,1981,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1996,1997,1998,1999,2001,2002,2003,2004,2005,2007,2009,2011,2013,2014,2015,2016,2017,2018,2019,2020,2021,2022,2023,2024,2025,2026,2027,2028,2149,2152,2155,2158,2172,2178,2188,2223,2252,2279,2288,2352,2715,2733,2767,2805,2823,3083,3089,3095,3116,3240,3260,3266,3270,3276,3311,3406,3496,3516,3571,3583,3609,3616", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,350,405,467,531,601,662,737,813,890,968,1213,1295,1371,1447,1524,1602,1708,1814,1893,1973,2030,2088,2419,2494,2559,2625,2685,2746,2818,2891,2958,3026,3085,3144,3203,3262,3321,3375,3429,3482,3536,3590,3644,3698,3904,3983,4056,4130,4201,4273,4345,4418,4475,4533,4606,4680,4754,4829,4901,4974,5044,5115,5175,5236,5305,5374,5444,5518,5594,5658,5735,5811,5888,5953,6022,6099,6174,6243,6311,6388,6454,6515,6612,6677,6746,6845,6916,6975,7033,7090,7149,7213,7284,7356,7428,7500,7572,7639,7707,7775,7834,7897,7961,8051,8142,8202,8268,8335,8401,8471,8535,8588,8655,8716,8783,8896,8954,9017,9082,9147,9222,9295,9367,9411,9458,9504,9553,9614,9675,9736,9798,9862,9926,9990,10055,10118,10178,10239,10305,10364,10424,10486,10557,10617,10685,11327,11414,11504,11591,11679,11761,11844,11934,12025,13055,13113,13158,13224,13288,13345,13402,13456,16255,16303,16352,16403,16530,16877,16926,17156,18132,18659,18721,18781,18838,19046,19116,19194,19248,19318,19403,19451,19497,19558,19621,19687,19751,19822,19885,19950,20014,20075,20136,20188,20261,20335,20404,20479,20553,20627,20768,20838,29092,30994,31084,31172,31268,31358,32619,32708,32955,33236,33488,33773,34166,34643,34865,35087,35363,35590,35820,36050,36280,36510,36737,37156,37382,37807,38037,38465,38684,38967,39175,39306,39533,39959,40184,40611,40832,41257,41377,41653,41954,42278,42569,42883,43020,43151,43256,43498,43665,43869,44077,44348,44460,44572,44677,44794,45008,45154,45294,45380,45728,45816,46062,46480,46729,46811,46909,47566,47666,47918,48342,48597,48691,48780,49017,51041,51283,51385,51638,53794,64475,65991,76686,78214,79971,80597,81017,82278,83543,83799,84035,84582,85076,85681,85879,86459,87827,88202,88320,88858,89015,89211,89484,89740,89910,90051,90115,90480,90847,91523,91787,92125,92478,92572,92758,93064,93326,93451,93578,93817,94028,94147,94340,94517,94972,95153,95275,95534,95647,95834,95936,96043,96172,96447,96955,97451,98328,98622,99192,99341,100073,100245,100329,100665,100757,101035,107274,112645,112707,113285,113869,113960,114073,114302,114462,114614,114785,114951,115120,115287,115450,115693,115863,116036,116207,116481,116680,116885,117215,117639,117735,117831,117929,118029,118131,118233,118335,118437,118539,118639,118735,118847,118976,119099,119230,119361,119459,119573,119667,119807,119941,120037,120149,120249,120365,120461,120573,120673,120813,120949,121113,121243,121401,121551,121692,121836,121971,122083,122233,122361,122489,122625,122757,122887,123017,123129,123269,124173,124317,124455,124547,124637,124713,124817,124907,125009,125117,125225,125325,125405,125497,125595,125705,125757,125835,125941,126033,126137,126247,126369,126532,126689,130050,130150,130240,130350,130440,130681,130775,130881,130973,131073,131185,131299,131415,131531,131625,131739,131851,131953,132073,132195,132277,132381,132501,132627,132725,132819,132907,133019,133135,133257,133369,133544,133660,133746,133838,133950,134074,134141,134267,134335,134463,134607,134735,134804,134899,135014,135127,135226,135335,135446,135557,135658,135763,135863,135993,136084,136207,136301,136413,136499,136603,136699,136787,136905,137009,137113,137239,137327,137435,137535,137625,137735,137819,137921,138005,138059,138123,138229,138315,138425,138509,138629,143711,143829,143944,144024,144385,144618,145135,146100,147444,148805,149193,152036,162089,162810,164383,166041,166613,174795,175057,175257,175636,179914,180520,180749,180900,181115,182198,183955,187690,188434,190565,190905,192216,192419"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\b14fceacc679837ad10a9c3882fe074a\\transformed\\activity-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "263,291", "startColumns": "4,4", "startOffsets": "16974,18372", "endColumns": "41,59", "endOffsets": "17011,18427"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\9a1fb8b6ab37a7fab4911fe988a07ef0\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "329", "startColumns": "4", "startOffsets": "20960", "endColumns": "82", "endOffsets": "21038"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\d6076e1149a51435d3ade43e5e15b90e\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "292", "startColumns": "4", "startOffsets": "18432", "endColumns": "53", "endOffsets": "18481"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\e25de8567adeb8e4d45f491e957ae728\\transformed\\appcompat-resources-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2189,2205,2211,3407,3423", "startColumns": "4,4,4,4,4", "startOffsets": "145140,145565,145743,183960,184371", "endLines": "2204,2210,2220,3422,3426", "endColumns": "24,24,24,24,24", "endOffsets": "145560,145738,146022,184366,184493"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\27df90de18a571932c46b95d0e259dc7\\transformed\\lifecycle-viewmodel-2.6.2\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "293", "startColumns": "4", "startOffsets": "18486", "endColumns": "49", "endOffsets": "18531"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\2d77937564f5b8a89d53275d66e6c6ae\\transformed\\lifecycle-runtime-2.6.2\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "290", "startColumns": "4", "startOffsets": "18329", "endColumns": "42", "endOffsets": "18367"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\26493aaaab252a5e482c1ec68ae1753b\\transformed\\drawee-3.6.0\\res\\values\\values.xml", "from": {"startLines": "2,136", "startColumns": "4,4", "startOffsets": "55,3906", "endLines": "135,218", "endColumns": "22,22", "endOffsets": "3901,5346"}, "to": {"startLines": "2887,3312", "startColumns": "4,4", "startOffsets": "169349,182203", "endLines": "3020,3394", "endColumns": "22,22", "endOffsets": "173195,183643"}}]}]}