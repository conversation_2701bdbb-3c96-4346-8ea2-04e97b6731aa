{"artifacts": [{"path": "BlePlx_autolinked_build/CMakeFiles/react_codegen_BlePlx.dir/./BlePlx-generated.cpp.o"}, {"path": "BlePlx_autolinked_build/CMakeFiles/react_codegen_BlePlx.dir/./react/renderer/components/BlePlx/BlePlxJSI-generated.cpp.o"}, {"path": "BlePlx_autolinked_build/CMakeFiles/react_codegen_BlePlx.dir/./react/renderer/components/BlePlx/ComponentDescriptors.cpp.o"}, {"path": "BlePlx_autolinked_build/CMakeFiles/react_codegen_BlePlx.dir/./react/renderer/components/BlePlx/EventEmitters.cpp.o"}, {"path": "BlePlx_autolinked_build/CMakeFiles/react_codegen_BlePlx.dir/./react/renderer/components/BlePlx/Props.cpp.o"}, {"path": "BlePlx_autolinked_build/CMakeFiles/react_codegen_BlePlx.dir/./react/renderer/components/BlePlx/ShadowNodes.cpp.o"}, {"path": "BlePlx_autolinked_build/CMakeFiles/react_codegen_BlePlx.dir/./react/renderer/components/BlePlx/States.cpp.o"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_compile_options", "target_compile_reactnative_options", "target_compile_definitions", "target_include_directories", "target_link_libraries"], "files": ["C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/CMakeLists.txt", "C:/BLE/node_modules/react-native/ReactCommon/cmake-utils/react-native-flags.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 11, "parent": 0}, {"command": 2, "file": 0, "line": 28, "parent": 0}, {"command": 1, "file": 1, "line": 30, "parent": 2}, {"command": 3, "file": 1, "line": 33, "parent": 2}, {"command": 4, "file": 0, "line": 17, "parent": 0}, {"command": 5, "file": 0, "line": 19, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC"}, {"backtrace": 3, "fragment": "-Wall"}, {"backtrace": 3, "fragment": "-Werror"}, {"backtrace": 3, "fragment": "-fexceptions"}, {"backtrace": 3, "fragment": "-frtti"}, {"backtrace": 3, "fragment": "-std=c++20"}, {"backtrace": 3, "fragment": "-DFOLLY_NO_CONFIG=1"}, {"backtrace": 3, "fragment": "-DLOG_TAG=\\\"ReactNative\\\""}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_CLOCK_GETTIME=1"}, {"backtrace": 0, "fragment": "-DFOLLY_USE_LIBCPP=1"}, {"backtrace": 0, "fragment": "-DFOLLY_CFG_NO_COROUTINES=1"}, {"backtrace": 0, "fragment": "-DFOLLY_MOBILE=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_RECVMMSG=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_PTHREAD=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_XSI_STRERROR_R=1"}], "defines": [{"backtrace": 4, "define": "RN_SERIALIZABLE_STATE"}], "includes": [{"backtrace": 5, "path": "C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/."}, {"backtrace": 5, "path": "C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx"}, {"backtrace": 6, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.14.3/transforms/a18451f415b73841e30da63b3d9805af/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"}, {"backtrace": 6, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/jsi/include"}, {"backtrace": 6, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.14.3/transforms/cba9d40536536f0777207a470c313f6b/transformed/react-android-0.81.1-debug/prefab/modules/reactnative/include"}], "language": "CXX", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6], "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "id": "react_codegen_BlePlx::@0d03ad0a4a63aefc401b", "name": "react_codegen_BlePlx", "paths": {"build": "BlePlx_autolinked_build", "source": "C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/BlePlx-generated.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx/BlePlxJSI-generated.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx/ComponentDescriptors.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx/EventEmitters.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx/Props.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx/ShadowNodes.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx/States.cpp", "sourceGroupIndex": 0}], "type": "OBJECT_LIBRARY"}