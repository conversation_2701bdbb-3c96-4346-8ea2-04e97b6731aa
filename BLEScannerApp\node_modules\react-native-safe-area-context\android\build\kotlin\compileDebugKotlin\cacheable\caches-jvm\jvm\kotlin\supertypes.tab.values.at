/ Header Record For PersistentHashMapValueStorage* )com.facebook.react.uimanager.events.Event8 7com.th3rdwave.safeareacontext.NativeSafeAreaContextSpec$ #com.facebook.react.BaseReactPackage] ,com.facebook.react.views.view.ReactViewGroup/android.view.ViewTreeObserver.OnPreDrawListenerr -com.facebook.react.uimanager.ViewGroupManagerCcom.facebook.react.viewmanagers.RNCSafeAreaProviderManagerInterface] ,com.facebook.react.views.view.ReactViewGroup/android.view.ViewTreeObserver.OnPreDrawListener kotlin.Enum/ .com.facebook.react.views.view.ReactViewManager kotlin.Enum. -com.facebook.react.uimanager.LayoutShadowNode