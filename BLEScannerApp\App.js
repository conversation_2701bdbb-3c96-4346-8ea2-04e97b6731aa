import React, { useState, useEffect } from 'react';
import {
  SafeAreaView,
  StyleSheet,
  Text,
  FlatList,
  Button,
  View,
  PermissionsAndroid,
  Platform,
  NativeModules,
  DeviceEventEmitter,
  ScrollView,
} from 'react-native';

const { BeaconXModule } = NativeModules;

const App = () => {
  const [devices, setDevices] = useState([]);
  const [scanning, setScanning] = useState(false);

  useEffect(() => {
    requestPermissions();
    setupEventListeners();
    
    return () => {
      DeviceEventEmitter.removeAllListeners('onScanStart');
      DeviceEventEmitter.removeAllListeners('onDeviceFound');
      DeviceEventEmitter.removeAllListeners('onScanStop');
    };
  }, []);

  const requestPermissions = async () => {
    if (Platform.OS === 'android') {
      const granted = await PermissionsAndroid.requestMultiple([
        PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
        PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT,
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION,
      ]);
      console.log('Permissions:', granted);
    }
  };

  const setupEventListeners = () => {
    DeviceEventEmitter.addListener('onScanStart', () => {
      console.log('Scan started');
      setScanning(true);
      setDevices([]);
    });

    DeviceEventEmitter.addListener('onDeviceFound', (device) => {
      console.log('Device found:', device);
      setDevices(prev => {
        const exists = prev.find(d => d.mac === device.mac);
        if (!exists) {
          return [...prev, device];
        } else {
          return prev.map(d => d.mac === device.mac ? device : d);
        }
      });
    });

    DeviceEventEmitter.addListener('onScanStop', () => {
      console.log('Scan stopped');
      setScanning(false);
    });
  };

  const startScan = async () => {
    try {
      await BeaconXModule.startScan();
      setTimeout(() => {
        stopScan();
      }, 30000);
    } catch (error) {
      console.error('Error starting scan:', error);
      setScanning(false);
    }
  };

  const stopScan = async () => {
    try {
      await BeaconXModule.stopScan();
    } catch (error) {
      console.error('Error stopping scan:', error);
    }
  };

  const renderFrameData = (frameData) => {
    const { frameType, uidData, urlData, tlmData, ibeaconData, thData, axisData } = frameData;
    
    return (
      <View style={styles.frameContainer} key={frameData.type}>
        <Text style={styles.frameTitle}>📡 {frameType}</Text>
        
        {uidData && (
          <View style={styles.frameDetails}>
            <Text style={styles.frameText}>Namespace: {uidData.namespace}</Text>
            <Text style={styles.frameText}>Instance: {uidData.instance}</Text>
            <Text style={styles.frameText}>TX Power: {uidData.txPower} dBm</Text>
          </View>
        )}
        
        {urlData && (
          <View style={styles.frameDetails}>
            <Text style={styles.frameText}>URL: {urlData.url}</Text>
            <Text style={styles.frameText}>TX Power: {urlData.txPower} dBm</Text>
          </View>
        )}
        
        {tlmData && (
          <View style={styles.frameDetails}>
            <Text style={styles.frameText}>Version: {tlmData.version}</Text>
            <Text style={styles.frameText}>Battery: {tlmData.battery}</Text>
            <Text style={styles.frameText}>Temperature: {tlmData.temperature}</Text>
            <Text style={styles.frameText}>Adv Count: {tlmData.advCount}</Text>
            <Text style={styles.frameText}>Sec Count: {tlmData.secCount}</Text>
          </View>
        )}
        
        {ibeaconData && (
          <View style={styles.frameDetails}>
            <Text style={styles.frameText}>UUID: {ibeaconData.uuid}</Text>
            <Text style={styles.frameText}>Major: {ibeaconData.major}</Text>
            <Text style={styles.frameText}>Minor: {ibeaconData.minor}</Text>
            <Text style={styles.frameText}>TX Power: {ibeaconData.txPower} dBm</Text>
            <Text style={styles.frameText}>Ranging: {ibeaconData.rangingData} dBm</Text>
          </View>
        )}
        
        {thData && (
          <View style={styles.frameDetails}>
            <Text style={styles.frameText}>🌡️ Temperature: {thData.temperature}°C</Text>
            <Text style={styles.frameText}>💧 Humidity: {thData.humidity}%</Text>
            <Text style={styles.frameText}>TX Power: {thData.txPower} dBm</Text>
          </View>
        )}
        
        {axisData && (
          <View style={styles.frameDetails}>
            <Text style={styles.frameText}>📊 X: {axisData.xData}</Text>
            <Text style={styles.frameText}>📊 Y: {axisData.yData}</Text>
            <Text style={styles.frameText}>📊 Z: {axisData.zData}</Text>
            <Text style={styles.frameText}>Data Rate: {axisData.dataRate}</Text>
            <Text style={styles.frameText}>Scale: {axisData.scale}</Text>
            <Text style={styles.frameText}>Sensitivity: {axisData.sensitivity}</Text>
          </View>
        )}
      </View>
    );
  };

  const renderDevice = ({ item }) => (
    <View style={styles.deviceItem}>
      <Text style={styles.deviceName}>
        🔵 {item.name || 'BeaconX Pro'}
      </Text>
      
      <View style={styles.deviceInfo}>
        <Text style={styles.infoText}>MAC: {item.mac}</Text>
        <Text style={styles.infoText}>RSSI: {item.rssi} dBm</Text>
        {item.uuid && <Text style={styles.infoText}>🆔 UUID: {item.uuid}</Text>}
        {item.major >= 0 && <Text style={styles.infoText}>📍 Major: {item.major}</Text>}
        {item.minor >= 0 && <Text style={styles.infoText}>📍 Minor: {item.minor}</Text>}
        {item.battery >= 0 && <Text style={styles.infoText}>🔋 Battery: {item.battery} mV</Text>}
        {item.intervalTime > 0 && <Text style={styles.infoText}>⏱️ Interval: {item.intervalTime} ms</Text>}
        {item.txPower !== 0 && <Text style={styles.infoText}>📡 TX Power: {item.txPower} dBm</Text>}
        {item.tamperState >= 0 && (
          <Text style={styles.infoText}>
            🔒 Tamper: {item.tamperState > 0 ? 'Alert' : 'Normal'}
          </Text>
        )}
        {item.ambientLightState >= 0 && (
          <Text style={styles.infoText}>
            💡 Light: {item.ambientLightState > 0 ? 'Detected' : 'Not detected'}
          </Text>
        )}

        {/* Dati Eddystone URL */}
        {item.url && <Text style={styles.infoText}>🔗 URL: {item.url}</Text>}

        {/* Dati Eddystone TLM */}
        {item.temperature !== undefined && !isNaN(item.temperature) && (
          <Text style={styles.infoText}>🌡️ Temperature: {item.temperature.toFixed(1)}°C</Text>
        )}
        {item.advCount >= 0 && <Text style={styles.infoText}>📊 ADV Count: {item.advCount}</Text>}
        {item.runningTime >= 0 && <Text style={styles.infoText}>⏱️ Running Time: {Math.floor(item.runningTime / 3600)}h {Math.floor((item.runningTime % 3600) / 60)}m</Text>}

        {/* Dati Eddystone UID */}
        {item.namespace && <Text style={styles.infoText}>🏷️ Namespace: {item.namespace}</Text>}
        {item.instanceId && <Text style={styles.infoText}>🆔 Instance: {item.instanceId}</Text>}
      </View>

      <ScrollView style={styles.framesContainer}>
        {item.validData && item.validData.map(frameData => renderFrameData(frameData))}
      </ScrollView>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <Text style={styles.title}>BeaconX Pro Scanner</Text>
      
      <Button
        title={scanning ? 'Scansione in corso...' : 'Avvia Scansione BeaconX'}
        onPress={startScan}
        disabled={scanning}
      />
      
      <Text style={styles.counter}>
        Beacon trovati: {devices.length}
      </Text>
      
      <FlatList
        data={devices}
        renderItem={renderDevice}
        keyExtractor={item => item.mac}
        style={styles.list}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#2196F3',
  },
  counter: {
    fontSize: 16,
    textAlign: 'center',
    marginVertical: 10,
    color: '#666',
  },
  list: {
    flex: 1,
    marginTop: 16,
  },
  deviceItem: {
    padding: 16,
    marginBottom: 12,
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
    elevation: 2,
  },
  deviceName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  deviceInfo: {
    marginBottom: 12,
  },
  infoText: {
    fontSize: 14,
    color: '#555',
    marginBottom: 2,
  },
  framesContainer: {
    maxHeight: 300,
  },
  frameContainer: {
    backgroundColor: '#e3f2fd',
    padding: 12,
    marginBottom: 8,
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: '#1976d2',
  },
  frameTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1976d2',
    marginBottom: 6,
  },
  frameDetails: {
    marginLeft: 8,
  },
  frameText: {
    fontSize: 12,
    color: '#333',
    marginBottom: 2,
  },
});

export default App;
