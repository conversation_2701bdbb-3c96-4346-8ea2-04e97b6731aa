# CMAKE generated file: DO NOT EDIT!
# Generated by CMake Version 3.22
cmake_policy(SET CMP0009 NEW)

# input_SRC at C:/BLE/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:55 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/BLE/android/app/build/generated/autolinking/src/main/jni/*.cpp")
set(OLD_GLOB
  "C:/BLE/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- G<PERSON>O<PERSON> mismatch!")
  file(TOUCH_NOCREATE "C:/BLE/android/app/.cxx/Debug/5t2fl393/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# override_cpp_SRC at C:/BLE/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:50 (file)
# input_SRC at C:/BLE/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:55 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/BLE/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/*.cpp")
set(OLD_GLOB
  "C:/BLE/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/BLE/android/app/.cxx/Debug/5t2fl393/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/BlePlx-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/BLE/android/app/.cxx/Debug/5t2fl393/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx/*.cpp")
set(OLD_GLOB
  "C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx/BlePlxJSI-generated.cpp"
  "C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx/ComponentDescriptors.cpp"
  "C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx/EventEmitters.cpp"
  "C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx/Props.cpp"
  "C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx/ShadowNodes.cpp"
  "C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-ble-plx/android/build/generated/source/codegen/jni/react/renderer/components/BlePlx/States.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/BLE/android/app/.cxx/Debug/5t2fl393/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:12 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/*.cpp")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/BLE/android/app/.cxx/Debug/5t2fl393/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:12 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp/react/renderer/components/safeareacontext/*.cpp")
set(OLD_GLOB
  "C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp"
  "C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/BLE/android/app/.cxx/Debug/5t2fl393/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CODEGEN_SRCS at C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:13 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/safeareacontext-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/BLE/android/app/.cxx/Debug/5t2fl393/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CODEGEN_SRCS at C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:13 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/*.cpp")
set(OLD_GLOB
  "C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp"
  "C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp"
  "C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp"
  "C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp"
  "C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp"
  "C:/Users/<USER>/Desktop/app for beacons/BeaconL01/BLEScannerApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/BLE/android/app/.cxx/Debug/5t2fl393/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()
