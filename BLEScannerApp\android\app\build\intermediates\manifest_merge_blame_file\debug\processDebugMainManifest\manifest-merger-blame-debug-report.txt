1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.blescannerapp"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="36" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->C:\BLE\android\app\src\debug\AndroidManifest.xml:5:5-77
11-->C:\BLE\android\app\src\debug\AndroidManifest.xml:5:22-75
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\BLE\android\app\src\main\AndroidManifest.xml:4:5-67
12-->C:\BLE\android\app\src\main\AndroidManifest.xml:4:22-64
13    <uses-permission
13-->C:\BLE\android\app\src\main\AndroidManifest.xml:5:5-68
14        android:name="android.permission.BLUETOOTH"
14-->C:\BLE\android\app\src\main\AndroidManifest.xml:5:22-65
15        android:maxSdkVersion="30" />
15-->[com.polidea.rxandroidble2:rxandroidble:1.17.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c92294ba8f0f324f556f7fb61758a6c5\transformed\rxandroidble-1.17.2\AndroidManifest.xml:12:9-35
16    <uses-permission
16-->C:\BLE\android\app\src\main\AndroidManifest.xml:6:5-74
17        android:name="android.permission.BLUETOOTH_ADMIN"
17-->C:\BLE\android\app\src\main\AndroidManifest.xml:6:22-71
18        android:maxSdkVersion="30" />
18-->[com.polidea.rxandroidble2:rxandroidble:1.17.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c92294ba8f0f324f556f7fb61758a6c5\transformed\rxandroidble-1.17.2\AndroidManifest.xml:15:9-35
19    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
19-->C:\BLE\android\app\src\main\AndroidManifest.xml:7:5-81
19-->C:\BLE\android\app\src\main\AndroidManifest.xml:7:22-78
20    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
20-->C:\BLE\android\app\src\main\AndroidManifest.xml:8:5-79
20-->C:\BLE\android\app\src\main\AndroidManifest.xml:8:22-76
21    <uses-permission
21-->C:\BLE\android\app\src\main\AndroidManifest.xml:9:5-73
22        android:name="android.permission.BLUETOOTH_SCAN"
22-->C:\BLE\android\app\src\main\AndroidManifest.xml:9:22-70
23        android:usesPermissionFlags="neverForLocation" />
23-->[com.polidea.rxandroidble2:rxandroidble:1.17.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c92294ba8f0f324f556f7fb61758a6c5\transformed\rxandroidble-1.17.2\AndroidManifest.xml:24:9-55
24    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
24-->C:\BLE\android\app\src\main\AndroidManifest.xml:10:5-76
24-->C:\BLE\android\app\src\main\AndroidManifest.xml:10:22-73
25
26    <uses-feature
26-->C:\BLE\android\app\src\main\AndroidManifest.xml:12:5-14:35
27        android:name="android.hardware.bluetooth_le"
27-->C:\BLE\android\app\src\main\AndroidManifest.xml:13:9-53
28        android:required="true" />
28-->C:\BLE\android\app\src\main\AndroidManifest.xml:14:9-32
29
30    <permission
30-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c0bb43adce9f553621f0b37557791d2d\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
31        android:name="com.blescannerapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
31-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c0bb43adce9f553621f0b37557791d2d\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
32        android:protectionLevel="signature" />
32-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c0bb43adce9f553621f0b37557791d2d\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
33
34    <uses-permission android:name="com.blescannerapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- required for API 23 - 30, no android:maxSdkVersion because of a potential breaking change -->
34-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c0bb43adce9f553621f0b37557791d2d\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
34-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c0bb43adce9f553621f0b37557791d2d\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
35    <!-- TODO: add android:maxSdkVersion on 2.0.0 -->
36    <uses-permission-sdk-23 android:name="android.permission.ACCESS_COARSE_LOCATION" />
36-->[com.polidea.rxandroidble2:rxandroidble:1.17.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c92294ba8f0f324f556f7fb61758a6c5\transformed\rxandroidble-1.17.2\AndroidManifest.xml:18:5-88
36-->[com.polidea.rxandroidble2:rxandroidble:1.17.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c92294ba8f0f324f556f7fb61758a6c5\transformed\rxandroidble-1.17.2\AndroidManifest.xml:18:29-85
37    <uses-permission-sdk-23 android:name="android.permission.ACCESS_FINE_LOCATION" />
37-->[com.polidea.rxandroidble2:rxandroidble:1.17.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c92294ba8f0f324f556f7fb61758a6c5\transformed\rxandroidble-1.17.2\AndroidManifest.xml:19:5-86
37-->[com.polidea.rxandroidble2:rxandroidble:1.17.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c92294ba8f0f324f556f7fb61758a6c5\transformed\rxandroidble-1.17.2\AndroidManifest.xml:19:29-83
38
39    <application
39-->C:\BLE\android\app\src\main\AndroidManifest.xml:16:5-36:19
40        android:name="com.blescannerapp.MainApplication"
40-->C:\BLE\android\app\src\main\AndroidManifest.xml:17:7-38
41        android:allowBackup="false"
41-->C:\BLE\android\app\src\main\AndroidManifest.xml:21:7-34
42        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c0bb43adce9f553621f0b37557791d2d\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
43        android:debuggable="true"
44        android:extractNativeLibs="false"
45        android:icon="@mipmap/ic_launcher"
45-->C:\BLE\android\app\src\main\AndroidManifest.xml:19:7-41
46        android:label="@string/app_name"
46-->C:\BLE\android\app\src\main\AndroidManifest.xml:18:7-39
47        android:roundIcon="@mipmap/ic_launcher_round"
47-->C:\BLE\android\app\src\main\AndroidManifest.xml:20:7-52
48        android:theme="@style/AppTheme"
48-->C:\BLE\android\app\src\main\AndroidManifest.xml:22:7-38
49        android:usesCleartextTraffic="true" >
49-->C:\BLE\android\app\src\debug\AndroidManifest.xml:8:9-44
50        <activity
50-->C:\BLE\android\app\src\main\AndroidManifest.xml:24:7-35:18
51            android:name="com.blescannerapp.MainActivity"
51-->C:\BLE\android\app\src\main\AndroidManifest.xml:25:9-37
52            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
52-->C:\BLE\android\app\src\main\AndroidManifest.xml:27:9-118
53            android:exported="true"
53-->C:\BLE\android\app\src\main\AndroidManifest.xml:30:9-32
54            android:label="@string/app_name"
54-->C:\BLE\android\app\src\main\AndroidManifest.xml:26:9-41
55            android:launchMode="singleTask"
55-->C:\BLE\android\app\src\main\AndroidManifest.xml:28:9-40
56            android:windowSoftInputMode="adjustResize" >
56-->C:\BLE\android\app\src\main\AndroidManifest.xml:29:9-51
57            <intent-filter>
57-->C:\BLE\android\app\src\main\AndroidManifest.xml:31:9-34:25
58                <action android:name="android.intent.action.MAIN" />
58-->C:\BLE\android\app\src\main\AndroidManifest.xml:32:13-65
58-->C:\BLE\android\app\src\main\AndroidManifest.xml:32:21-62
59
60                <category android:name="android.intent.category.LAUNCHER" />
60-->C:\BLE\android\app\src\main\AndroidManifest.xml:33:13-73
60-->C:\BLE\android\app\src\main\AndroidManifest.xml:33:23-70
61            </intent-filter>
62        </activity>
63        <activity
63-->[com.facebook.react:react-android:0.81.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cba9d40536536f0777207a470c313f6b\transformed\react-android-0.81.1-debug\AndroidManifest.xml:19:9-21:40
64            android:name="com.facebook.react.devsupport.DevSettingsActivity"
64-->[com.facebook.react:react-android:0.81.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cba9d40536536f0777207a470c313f6b\transformed\react-android-0.81.1-debug\AndroidManifest.xml:20:13-77
65            android:exported="false" />
65-->[com.facebook.react:react-android:0.81.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cba9d40536536f0777207a470c313f6b\transformed\react-android-0.81.1-debug\AndroidManifest.xml:21:13-37
66
67        <provider
67-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\87d1f4dfe58076090bb2d88539c633aa\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
68            android:name="androidx.startup.InitializationProvider"
68-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\87d1f4dfe58076090bb2d88539c633aa\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
69            android:authorities="com.blescannerapp.androidx-startup"
69-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\87d1f4dfe58076090bb2d88539c633aa\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
70            android:exported="false" >
70-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\87d1f4dfe58076090bb2d88539c633aa\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
71            <meta-data
71-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\87d1f4dfe58076090bb2d88539c633aa\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
72                android:name="androidx.emoji2.text.EmojiCompatInitializer"
72-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\87d1f4dfe58076090bb2d88539c633aa\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
73                android:value="androidx.startup" />
73-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\87d1f4dfe58076090bb2d88539c633aa\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
74            <meta-data
74-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d2340342a38e2ad07ed5f1cac7e16edb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
75                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
75-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d2340342a38e2ad07ed5f1cac7e16edb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
76                android:value="androidx.startup" />
76-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d2340342a38e2ad07ed5f1cac7e16edb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
77            <meta-data
77-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
78                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
79                android:value="androidx.startup" />
79-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
80        </provider>
81
82        <receiver
82-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
83            android:name="androidx.profileinstaller.ProfileInstallReceiver"
83-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
84            android:directBootAware="false"
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
85            android:enabled="true"
85-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
86            android:exported="true"
86-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
87            android:permission="android.permission.DUMP" >
87-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
88            <intent-filter>
88-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
89                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
89-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
89-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
90            </intent-filter>
91            <intent-filter>
91-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
92                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
92-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
92-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
93            </intent-filter>
94            <intent-filter>
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
95                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
95-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
95-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
96            </intent-filter>
97            <intent-filter>
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
98                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
98-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
98-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
99            </intent-filter>
100        </receiver>
101
102        <meta-data
102-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bcd337e69414c9658176be4bc6b3a30f\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
103            android:name="com.facebook.soloader.enabled"
103-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bcd337e69414c9658176be4bc6b3a30f\transformed\soloader-0.12.1\AndroidManifest.xml:13:13-57
104            android:value="false" />
104-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bcd337e69414c9658176be4bc6b3a30f\transformed\soloader-0.12.1\AndroidManifest.xml:14:13-34
105    </application>
106
107</manifest>
