1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.blescannerapp"
4    android:versionCode="1"
5    android:versionName="1.0" > <!-- Override per forzare l'uso di MKBleLib con minSdk 24 -->
6    <uses-sdk
6-->C:\BLE\android\app\src\main\AndroidManifest.xml:5:5-58
7        android:minSdkVersion="24"
8        android:targetSdkVersion="36" />
9
10    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
10-->C:\BLE\android\app\src\debug\AndroidManifest.xml:5:5-77
10-->C:\BLE\android\app\src\debug\AndroidManifest.xml:5:22-75
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\BLE\android\app\src\main\AndroidManifest.xml:7:5-67
11-->C:\BLE\android\app\src\main\AndroidManifest.xml:7:22-64
12    <uses-permission
12-->C:\BLE\android\app\src\main\AndroidManifest.xml:8:5-68
13        android:name="android.permission.BLUETOOTH"
13-->C:\BLE\android\app\src\main\AndroidManifest.xml:8:22-65
14        android:maxSdkVersion="30" />
14-->[:mokosupport] C:\BLE\android\mokosupport\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:9-35
15    <uses-permission
15-->C:\BLE\android\app\src\main\AndroidManifest.xml:9:5-74
16        android:name="android.permission.BLUETOOTH_ADMIN"
16-->C:\BLE\android\app\src\main\AndroidManifest.xml:9:22-71
17        android:maxSdkVersion="30" />
17-->[:mokosupport] C:\BLE\android\mokosupport\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:9-35
18    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
18-->C:\BLE\android\app\src\main\AndroidManifest.xml:10:5-81
18-->C:\BLE\android\app\src\main\AndroidManifest.xml:10:22-78
19    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
19-->C:\BLE\android\app\src\main\AndroidManifest.xml:11:5-79
19-->C:\BLE\android\app\src\main\AndroidManifest.xml:11:22-76
20    <uses-permission
20-->C:\BLE\android\app\src\main\AndroidManifest.xml:12:5-73
21        android:name="android.permission.BLUETOOTH_SCAN"
21-->C:\BLE\android\app\src\main\AndroidManifest.xml:12:22-70
22        android:usesPermissionFlags="neverForLocation" />
22-->[com.polidea.rxandroidble2:rxandroidble:1.17.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c92294ba8f0f324f556f7fb61758a6c5\transformed\rxandroidble-1.17.2\AndroidManifest.xml:24:9-55
23    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
23-->C:\BLE\android\app\src\main\AndroidManifest.xml:13:5-76
23-->C:\BLE\android\app\src\main\AndroidManifest.xml:13:22-73
24
25    <uses-feature
25-->C:\BLE\android\app\src\main\AndroidManifest.xml:15:5-17:35
26        android:name="android.hardware.bluetooth_le"
26-->C:\BLE\android\app\src\main\AndroidManifest.xml:16:9-53
27        android:required="true" /> <!-- SDCard中创建与删除文件权限 -->
27-->C:\BLE\android\app\src\main\AndroidManifest.xml:17:9-32
28    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" /> <!-- 向SDCard写入数据权限 -->
28-->[:mokosupport] C:\BLE\android\mokosupport\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-10:47
28-->[:mokosupport] C:\BLE\android\mokosupport\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-68
29    <uses-permission
29-->[:mokosupport] C:\BLE\android\mokosupport\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-14:38
30        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
30-->[:mokosupport] C:\BLE\android\mokosupport\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-65
31        android:maxSdkVersion="28" />
31-->[:mokosupport] C:\BLE\android\mokosupport\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-35
32    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
33
34    <permission
34-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c0bb43adce9f553621f0b37557791d2d\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
35        android:name="com.blescannerapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
35-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c0bb43adce9f553621f0b37557791d2d\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
36        android:protectionLevel="signature" />
36-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c0bb43adce9f553621f0b37557791d2d\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
37
38    <uses-permission android:name="com.blescannerapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- required for API 23 - 30, no android:maxSdkVersion because of a potential breaking change -->
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c0bb43adce9f553621f0b37557791d2d\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c0bb43adce9f553621f0b37557791d2d\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
39    <!-- TODO: add android:maxSdkVersion on 2.0.0 -->
40    <uses-permission-sdk-23 android:name="android.permission.ACCESS_COARSE_LOCATION" />
40-->[com.polidea.rxandroidble2:rxandroidble:1.17.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c92294ba8f0f324f556f7fb61758a6c5\transformed\rxandroidble-1.17.2\AndroidManifest.xml:18:5-88
40-->[com.polidea.rxandroidble2:rxandroidble:1.17.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c92294ba8f0f324f556f7fb61758a6c5\transformed\rxandroidble-1.17.2\AndroidManifest.xml:18:29-85
41    <uses-permission-sdk-23 android:name="android.permission.ACCESS_FINE_LOCATION" />
41-->[com.polidea.rxandroidble2:rxandroidble:1.17.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c92294ba8f0f324f556f7fb61758a6c5\transformed\rxandroidble-1.17.2\AndroidManifest.xml:19:5-86
41-->[com.polidea.rxandroidble2:rxandroidble:1.17.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c92294ba8f0f324f556f7fb61758a6c5\transformed\rxandroidble-1.17.2\AndroidManifest.xml:19:29-83
42
43    <application
43-->C:\BLE\android\app\src\main\AndroidManifest.xml:19:5-39:19
44        android:name="com.blescannerapp.MainApplication"
44-->C:\BLE\android\app\src\main\AndroidManifest.xml:20:7-38
45        android:allowBackup="false"
45-->C:\BLE\android\app\src\main\AndroidManifest.xml:24:7-34
46        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
46-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c0bb43adce9f553621f0b37557791d2d\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
47        android:debuggable="true"
48        android:extractNativeLibs="false"
49        android:icon="@mipmap/ic_launcher"
49-->C:\BLE\android\app\src\main\AndroidManifest.xml:22:7-41
50        android:label="@string/app_name"
50-->C:\BLE\android\app\src\main\AndroidManifest.xml:21:7-39
51        android:roundIcon="@mipmap/ic_launcher_round"
51-->C:\BLE\android\app\src\main\AndroidManifest.xml:23:7-52
52        android:supportsRtl="true"
52-->[:mokosupport] C:\BLE\android\mokosupport\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:9-35
53        android:theme="@style/AppTheme"
53-->C:\BLE\android\app\src\main\AndroidManifest.xml:25:7-38
54        android:usesCleartextTraffic="true" >
54-->C:\BLE\android\app\src\debug\AndroidManifest.xml:8:9-44
55        <activity
55-->C:\BLE\android\app\src\main\AndroidManifest.xml:27:7-38:18
56            android:name="com.blescannerapp.MainActivity"
56-->C:\BLE\android\app\src\main\AndroidManifest.xml:28:9-37
57            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
57-->C:\BLE\android\app\src\main\AndroidManifest.xml:30:9-118
58            android:exported="true"
58-->C:\BLE\android\app\src\main\AndroidManifest.xml:33:9-32
59            android:label="@string/app_name"
59-->C:\BLE\android\app\src\main\AndroidManifest.xml:29:9-41
60            android:launchMode="singleTask"
60-->C:\BLE\android\app\src\main\AndroidManifest.xml:31:9-40
61            android:windowSoftInputMode="adjustResize" >
61-->C:\BLE\android\app\src\main\AndroidManifest.xml:32:9-51
62            <intent-filter>
62-->C:\BLE\android\app\src\main\AndroidManifest.xml:34:9-37:25
63                <action android:name="android.intent.action.MAIN" />
63-->C:\BLE\android\app\src\main\AndroidManifest.xml:35:13-65
63-->C:\BLE\android\app\src\main\AndroidManifest.xml:35:21-62
64
65                <category android:name="android.intent.category.LAUNCHER" />
65-->C:\BLE\android\app\src\main\AndroidManifest.xml:36:13-73
65-->C:\BLE\android\app\src\main\AndroidManifest.xml:36:23-70
66            </intent-filter>
67        </activity>
68        <activity
68-->[com.facebook.react:react-android:0.81.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cba9d40536536f0777207a470c313f6b\transformed\react-android-0.81.1-debug\AndroidManifest.xml:19:9-21:40
69            android:name="com.facebook.react.devsupport.DevSettingsActivity"
69-->[com.facebook.react:react-android:0.81.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cba9d40536536f0777207a470c313f6b\transformed\react-android-0.81.1-debug\AndroidManifest.xml:20:13-77
70            android:exported="false" />
70-->[com.facebook.react:react-android:0.81.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cba9d40536536f0777207a470c313f6b\transformed\react-android-0.81.1-debug\AndroidManifest.xml:21:13-37
71
72        <provider
72-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\87d1f4dfe58076090bb2d88539c633aa\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
73            android:name="androidx.startup.InitializationProvider"
73-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\87d1f4dfe58076090bb2d88539c633aa\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
74            android:authorities="com.blescannerapp.androidx-startup"
74-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\87d1f4dfe58076090bb2d88539c633aa\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
75            android:exported="false" >
75-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\87d1f4dfe58076090bb2d88539c633aa\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
76            <meta-data
76-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\87d1f4dfe58076090bb2d88539c633aa\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
77                android:name="androidx.emoji2.text.EmojiCompatInitializer"
77-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\87d1f4dfe58076090bb2d88539c633aa\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
78                android:value="androidx.startup" />
78-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\87d1f4dfe58076090bb2d88539c633aa\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
79            <meta-data
79-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d2340342a38e2ad07ed5f1cac7e16edb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
80                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
80-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d2340342a38e2ad07ed5f1cac7e16edb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
81                android:value="androidx.startup" />
81-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d2340342a38e2ad07ed5f1cac7e16edb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
82            <meta-data
82-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
83                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
83-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
84                android:value="androidx.startup" />
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
85        </provider>
86
87        <receiver
87-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
88            android:name="androidx.profileinstaller.ProfileInstallReceiver"
88-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
89            android:directBootAware="false"
89-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
90            android:enabled="true"
90-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
91            android:exported="true"
91-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
92            android:permission="android.permission.DUMP" >
92-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
93            <intent-filter>
93-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
94                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
95            </intent-filter>
96            <intent-filter>
96-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
97                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
98            </intent-filter>
99            <intent-filter>
99-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
100                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
100-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
100-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
101            </intent-filter>
102            <intent-filter>
102-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
103                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
103-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
103-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
104            </intent-filter>
105        </receiver>
106        <!--
107		Service used to scan in background to emulate scanning with
108		PendingIntent on Android 4.3 - 7.x. Service will scan in background with given
109		settings and filters.
110        -->
111        <service
111-->[no.nordicsemi.android.support.v18:scanner:1.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c3836b0d9c706713b5caff85ede7ed13\transformed\scanner-1.6.0\AndroidManifest.xml:41:9-43:40
112            android:name="no.nordicsemi.android.support.v18.scanner.ScannerService"
112-->[no.nordicsemi.android.support.v18:scanner:1.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c3836b0d9c706713b5caff85ede7ed13\transformed\scanner-1.6.0\AndroidManifest.xml:42:13-84
113            android:exported="false" />
113-->[no.nordicsemi.android.support.v18:scanner:1.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c3836b0d9c706713b5caff85ede7ed13\transformed\scanner-1.6.0\AndroidManifest.xml:43:13-37
114        <!--
115		The receiver used to scan with PendingIntent on Android 8+.
116		It will translate results from android.bluetooth.le package to
117		no.nordicsemi.android.support.v18, apply filters or perform batching,
118		depending on the settings.
119        -->
120        <receiver
120-->[no.nordicsemi.android.support.v18:scanner:1.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c3836b0d9c706713b5caff85ede7ed13\transformed\scanner-1.6.0\AndroidManifest.xml:51:9-58:20
121            android:name="no.nordicsemi.android.support.v18.scanner.PendingIntentReceiver"
121-->[no.nordicsemi.android.support.v18:scanner:1.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c3836b0d9c706713b5caff85ede7ed13\transformed\scanner-1.6.0\AndroidManifest.xml:52:13-91
122            android:exported="true" >
122-->[no.nordicsemi.android.support.v18:scanner:1.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c3836b0d9c706713b5caff85ede7ed13\transformed\scanner-1.6.0\AndroidManifest.xml:53:13-36
123            <intent-filter>
123-->[no.nordicsemi.android.support.v18:scanner:1.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c3836b0d9c706713b5caff85ede7ed13\transformed\scanner-1.6.0\AndroidManifest.xml:55:13-57:29
124                <action android:name="no.nordicsemi.android.support.v18.ACTION_FOUND" />
124-->[no.nordicsemi.android.support.v18:scanner:1.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c3836b0d9c706713b5caff85ede7ed13\transformed\scanner-1.6.0\AndroidManifest.xml:56:17-89
124-->[no.nordicsemi.android.support.v18:scanner:1.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c3836b0d9c706713b5caff85ede7ed13\transformed\scanner-1.6.0\AndroidManifest.xml:56:25-86
125            </intent-filter>
126        </receiver>
127
128        <meta-data
128-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bcd337e69414c9658176be4bc6b3a30f\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
129            android:name="com.facebook.soloader.enabled"
129-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bcd337e69414c9658176be4bc6b3a30f\transformed\soloader-0.12.1\AndroidManifest.xml:13:13-57
130            android:value="false" />
130-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bcd337e69414c9658176be4bc6b3a30f\transformed\soloader-0.12.1\AndroidManifest.xml:14:13-34
131    </application>
132
133</manifest>
