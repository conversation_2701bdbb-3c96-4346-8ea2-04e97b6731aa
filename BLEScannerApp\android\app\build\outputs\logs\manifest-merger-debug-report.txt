-- Merging decision tree log ---
manifest
ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:1:1-37:12
MERGED from C:\BLE\android\app\src\main\AndroidManifest.xml:1:1-37:12
INJECTED from C:\BLE\android\app\src\debug\AndroidManifest.xml:2:1-13:12
INJECTED from C:\BLE\android\app\src\debug\AndroidManifest.xml:2:1-13:12
INJECTED from C:\BLE\android\app\src\debug\AndroidManifest.xml:2:1-13:12
MERGED from [:react-native-safe-area-context] C:\Users\<USER>\Desktop\app for beacons\BeaconL01\BLEScannerApp\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-ble-plx] C:\Users\<USER>\Desktop\app for beacons\BeaconL01\BLEScannerApp\node_modules\react-native-ble-plx\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:react-android:0.81.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cba9d40536536f0777207a470c313f6b\transformed\react-android-0.81.1-debug\AndroidManifest.xml:2:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e25de8567adeb8e4d45f491e957ae728\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9a3d7109ece86782fa80f9daad928ce1\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b5a9279ab394007c461e1d5eb7c8a15b\transformed\fresco-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2beb0a9dccd2f38a95bd3aaff253e527\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\26493aaaab252a5e482c1ec68ae1753b\transformed\drawee-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bdf033f8191bed9f0968b36634e42735\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5d2ed1c89000055affd0f67b1daca23a\transformed\memory-type-native-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e8eb8c3a3e02a55099a51531e06fc988\transformed\memory-type-java-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\32210a73ead0455741e28000179b9a1b\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4b8006961dc02a38eb5bcd566f084bf2\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2e02bd53cfcbb93d5d5b13fb94d095e6\transformed\imagepipeline-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\799bfe10b3b0b3c95f6073934e84afe2\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b940052ab523ddc44fc2a5e90c3a45ef\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cbbdde1c5a1285ba713b008bbccccbec\transformed\urimod-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\283433f589a82f6b23953924ffbc4092\transformed\vito-source-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b231a45d9e76ba34de4abfcf2caec5b6\transformed\middleware-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\24b79bfe194ccb4e98084ce28b0d08b8\transformed\ui-common-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b2ca67d787aef4b1f7f6ac4349cb330e\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b14fceacc679837ad10a9c3882fe074a\transformed\activity-1.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1ca3663a1fa1b38f8b90bac9094d5e60\transformed\autofill-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f2c15e360ba8954837caf13e2707c423\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9c728651937b93c6d01fd529e7efa8df\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ca7e54b0aeaee2f1c24a77c0a680c20f\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\87d1f4dfe58076090bb2d88539c633aa\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ec6c0dbdbbf22d12e133a07aa67a308f\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\de784c246beadffb279447607bd137e2\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\182787fe4242e9c3e9c3abe9db40fae7\transformed\soloader-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c68acac0bc94023968ef369bb24f037d\transformed\fbcore-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\98e397952212d3e2136d89fae3b18646\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\207be528686cd3afd2f06f0670a2ed41\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\224821547994486db85d5b9f9d2b6143\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bdef8dd643bd18b55d676b6b836b8035\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c0bb43adce9f553621f0b37557791d2d\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27df90de18a571932c46b95d0e259dc7\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d2340342a38e2ad07ed5f1cac7e16edb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cc740a720b0f76a534131973a89dee17\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\91c01c8efba535e0ad95ed10639a132e\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2d77937564f5b8a89d53275d66e6c6ae\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ac2f20c823a194c54e5aa4cffebfe995\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d6076e1149a51435d3ade43e5e15b90e\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\368c46131c9ae14bd68d2909a201d213\transformed\ui-core-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:hermes-android:0.81.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\197577c9406afbc5eba8e8fb58fb092a\transformed\hermes-android-0.81.1-debug\AndroidManifest.xml:2:1-7:12
MERGED from [com.polidea.rxandroidble2:rxandroidble:1.17.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c92294ba8f0f324f556f7fb61758a6c5\transformed\rxandroidble-1.17.2\AndroidManifest.xml:2:1-27:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9a1fb8b6ab37a7fab4911fe988a07ef0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\60ccac169870d0bb8d93ec02bd82151f\transformed\tracing-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2e52a4f45d6867d7d53cffcab6f1e891\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5d22e286bd5c783499b72cf869d67293\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\727bb0c7cce11f5cd3a4b9879f6e0d7f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b9d230fcdfa30dbfce61bf3b9605d6b5\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2aeab61e6c79580a61ba42c63c36e8b9\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a18451f415b73841e30da63b3d9805af\transformed\fbjni-0.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bcd337e69414c9658176be4bc6b3a30f\transformed\soloader-0.12.1\AndroidManifest.xml:2:1-17:12
	package
		INJECTED from C:\BLE\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from C:\BLE\android\app\src\debug\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:2:5-51
	android:versionCode
		INJECTED from C:\BLE\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:4:5-67
	android:name
		ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:4:22-64
uses-permission#android.permission.BLUETOOTH
ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:5:5-68
MERGED from [com.polidea.rxandroidble2:rxandroidble:1.17.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c92294ba8f0f324f556f7fb61758a6c5\transformed\rxandroidble-1.17.2\AndroidManifest.xml:10:5-12:38
MERGED from [com.polidea.rxandroidble2:rxandroidble:1.17.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c92294ba8f0f324f556f7fb61758a6c5\transformed\rxandroidble-1.17.2\AndroidManifest.xml:10:5-12:38
	android:maxSdkVersion
		ADDED from [com.polidea.rxandroidble2:rxandroidble:1.17.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c92294ba8f0f324f556f7fb61758a6c5\transformed\rxandroidble-1.17.2\AndroidManifest.xml:12:9-35
	android:name
		ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:5:22-65
uses-permission#android.permission.BLUETOOTH_ADMIN
ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:6:5-74
MERGED from [com.polidea.rxandroidble2:rxandroidble:1.17.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c92294ba8f0f324f556f7fb61758a6c5\transformed\rxandroidble-1.17.2\AndroidManifest.xml:13:5-15:38
MERGED from [com.polidea.rxandroidble2:rxandroidble:1.17.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c92294ba8f0f324f556f7fb61758a6c5\transformed\rxandroidble-1.17.2\AndroidManifest.xml:13:5-15:38
	android:maxSdkVersion
		ADDED from [com.polidea.rxandroidble2:rxandroidble:1.17.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c92294ba8f0f324f556f7fb61758a6c5\transformed\rxandroidble-1.17.2\AndroidManifest.xml:15:9-35
	android:name
		ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:6:22-71
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:7:5-81
	android:name
		ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:7:22-78
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:8:5-79
	android:name
		ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:8:22-76
uses-permission#android.permission.BLUETOOTH_SCAN
ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:9:5-73
MERGED from [com.polidea.rxandroidble2:rxandroidble:1.17.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c92294ba8f0f324f556f7fb61758a6c5\transformed\rxandroidble-1.17.2\AndroidManifest.xml:22:5-25:31
MERGED from [com.polidea.rxandroidble2:rxandroidble:1.17.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c92294ba8f0f324f556f7fb61758a6c5\transformed\rxandroidble-1.17.2\AndroidManifest.xml:22:5-25:31
	android:usesPermissionFlags
		ADDED from [com.polidea.rxandroidble2:rxandroidble:1.17.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c92294ba8f0f324f556f7fb61758a6c5\transformed\rxandroidble-1.17.2\AndroidManifest.xml:24:9-55
	tools:targetApi
		ADDED from [com.polidea.rxandroidble2:rxandroidble:1.17.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c92294ba8f0f324f556f7fb61758a6c5\transformed\rxandroidble-1.17.2\AndroidManifest.xml:25:9-28
	android:name
		ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:9:22-70
uses-permission#android.permission.BLUETOOTH_CONNECT
ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:10:5-76
MERGED from [com.polidea.rxandroidble2:rxandroidble:1.17.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c92294ba8f0f324f556f7fb61758a6c5\transformed\rxandroidble-1.17.2\AndroidManifest.xml:21:5-76
MERGED from [com.polidea.rxandroidble2:rxandroidble:1.17.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c92294ba8f0f324f556f7fb61758a6c5\transformed\rxandroidble-1.17.2\AndroidManifest.xml:21:5-76
	android:name
		ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:10:22-73
uses-feature#android.hardware.bluetooth_le
ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:12:5-14:35
	android:required
		ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:14:9-32
	android:name
		ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:13:9-53
application
ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:16:5-36:19
MERGED from C:\BLE\android\app\src\main\AndroidManifest.xml:16:5-36:19
MERGED from C:\BLE\android\app\src\main\AndroidManifest.xml:16:5-36:19
INJECTED from C:\BLE\android\app\src\debug\AndroidManifest.xml:7:5-12:47
MERGED from [com.facebook.react:react-android:0.81.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cba9d40536536f0777207a470c313f6b\transformed\react-android-0.81.1-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.facebook.react:react-android:0.81.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cba9d40536536f0777207a470c313f6b\transformed\react-android-0.81.1-debug\AndroidManifest.xml:18:5-22:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\87d1f4dfe58076090bb2d88539c633aa\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\87d1f4dfe58076090bb2d88539c633aa\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c0bb43adce9f553621f0b37557791d2d\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c0bb43adce9f553621f0b37557791d2d\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d2340342a38e2ad07ed5f1cac7e16edb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d2340342a38e2ad07ed5f1cac7e16edb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9a1fb8b6ab37a7fab4911fe988a07ef0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9a1fb8b6ab37a7fab4911fe988a07ef0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\727bb0c7cce11f5cd3a4b9879f6e0d7f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\727bb0c7cce11f5cd3a4b9879f6e0d7f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bcd337e69414c9658176be4bc6b3a30f\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bcd337e69414c9658176be4bc6b3a30f\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
	android:extractNativeLibs
		INJECTED from C:\BLE\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c0bb43adce9f553621f0b37557791d2d\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:label
		ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:18:7-39
		ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:18:7-39
	tools:ignore
		ADDED from C:\BLE\android\app\src\debug\AndroidManifest.xml:11:9-48
	android:roundIcon
		ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:20:7-52
		ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:20:7-52
	tools:targetApi
		ADDED from C:\BLE\android\app\src\debug\AndroidManifest.xml:10:9-29
	android:icon
		ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:19:7-41
		ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:19:7-41
	android:allowBackup
		ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:21:7-34
		REJECTED from C:\BLE\android\app\src\main\AndroidManifest.xml:21:7-34
	android:theme
		ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:22:7-38
		ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:22:7-38
	tools:replace
		ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:23:7-42
	android:usesCleartextTraffic
		ADDED from C:\BLE\android\app\src\debug\AndroidManifest.xml:8:9-44
	android:name
		ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:17:7-38
		ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:17:7-38
activity#com.blescannerapp.MainActivity
ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:24:7-35:18
	android:label
		ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:26:9-41
	android:launchMode
		ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:28:9-40
	android:windowSoftInputMode
		ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:29:9-51
	android:exported
		ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:30:9-32
	android:configChanges
		ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:27:9-118
	android:name
		ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:25:9-37
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:31:9-34:25
action#android.intent.action.MAIN
ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:32:13-65
	android:name
		ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:32:21-62
category#android.intent.category.LAUNCHER
ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:33:13-73
	android:name
		ADDED from C:\BLE\android\app\src\main\AndroidManifest.xml:33:23-70
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from C:\BLE\android\app\src\debug\AndroidManifest.xml:5:5-77
MERGED from [com.facebook.react:react-android:0.81.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cba9d40536536f0777207a470c313f6b\transformed\react-android-0.81.1-debug\AndroidManifest.xml:16:5-78
MERGED from [com.facebook.react:react-android:0.81.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cba9d40536536f0777207a470c313f6b\transformed\react-android-0.81.1-debug\AndroidManifest.xml:16:5-78
	android:name
		ADDED from C:\BLE\android\app\src\debug\AndroidManifest.xml:5:22-75
uses-sdk
INJECTED from C:\BLE\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\BLE\android\app\src\debug\AndroidManifest.xml
INJECTED from C:\BLE\android\app\src\debug\AndroidManifest.xml
MERGED from [:react-native-safe-area-context] C:\Users\<USER>\Desktop\app for beacons\BeaconL01\BLEScannerApp\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] C:\Users\<USER>\Desktop\app for beacons\BeaconL01\BLEScannerApp\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-ble-plx] C:\Users\<USER>\Desktop\app for beacons\BeaconL01\BLEScannerApp\node_modules\react-native-ble-plx\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-ble-plx] C:\Users\<USER>\Desktop\app for beacons\BeaconL01\BLEScannerApp\node_modules\react-native-ble-plx\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:react-android:0.81.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cba9d40536536f0777207a470c313f6b\transformed\react-android-0.81.1-debug\AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.81.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cba9d40536536f0777207a470c313f6b\transformed\react-android-0.81.1-debug\AndroidManifest.xml:10:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e25de8567adeb8e4d45f491e957ae728\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e25de8567adeb8e4d45f491e957ae728\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9a3d7109ece86782fa80f9daad928ce1\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9a3d7109ece86782fa80f9daad928ce1\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b5a9279ab394007c461e1d5eb7c8a15b\transformed\fresco-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b5a9279ab394007c461e1d5eb7c8a15b\transformed\fresco-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2beb0a9dccd2f38a95bd3aaff253e527\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2beb0a9dccd2f38a95bd3aaff253e527\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\26493aaaab252a5e482c1ec68ae1753b\transformed\drawee-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\26493aaaab252a5e482c1ec68ae1753b\transformed\drawee-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bdf033f8191bed9f0968b36634e42735\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bdf033f8191bed9f0968b36634e42735\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5d2ed1c89000055affd0f67b1daca23a\transformed\memory-type-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5d2ed1c89000055affd0f67b1daca23a\transformed\memory-type-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e8eb8c3a3e02a55099a51531e06fc988\transformed\memory-type-java-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e8eb8c3a3e02a55099a51531e06fc988\transformed\memory-type-java-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\32210a73ead0455741e28000179b9a1b\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\32210a73ead0455741e28000179b9a1b\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4b8006961dc02a38eb5bcd566f084bf2\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4b8006961dc02a38eb5bcd566f084bf2\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2e02bd53cfcbb93d5d5b13fb94d095e6\transformed\imagepipeline-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2e02bd53cfcbb93d5d5b13fb94d095e6\transformed\imagepipeline-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\799bfe10b3b0b3c95f6073934e84afe2\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\799bfe10b3b0b3c95f6073934e84afe2\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b940052ab523ddc44fc2a5e90c3a45ef\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b940052ab523ddc44fc2a5e90c3a45ef\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cbbdde1c5a1285ba713b008bbccccbec\transformed\urimod-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cbbdde1c5a1285ba713b008bbccccbec\transformed\urimod-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\283433f589a82f6b23953924ffbc4092\transformed\vito-source-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\283433f589a82f6b23953924ffbc4092\transformed\vito-source-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b231a45d9e76ba34de4abfcf2caec5b6\transformed\middleware-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b231a45d9e76ba34de4abfcf2caec5b6\transformed\middleware-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\24b79bfe194ccb4e98084ce28b0d08b8\transformed\ui-common-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\24b79bfe194ccb4e98084ce28b0d08b8\transformed\ui-common-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b2ca67d787aef4b1f7f6ac4349cb330e\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b2ca67d787aef4b1f7f6ac4349cb330e\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b14fceacc679837ad10a9c3882fe074a\transformed\activity-1.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b14fceacc679837ad10a9c3882fe074a\transformed\activity-1.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1ca3663a1fa1b38f8b90bac9094d5e60\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1ca3663a1fa1b38f8b90bac9094d5e60\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f2c15e360ba8954837caf13e2707c423\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f2c15e360ba8954837caf13e2707c423\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9c728651937b93c6d01fd529e7efa8df\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9c728651937b93c6d01fd529e7efa8df\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ca7e54b0aeaee2f1c24a77c0a680c20f\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ca7e54b0aeaee2f1c24a77c0a680c20f\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\87d1f4dfe58076090bb2d88539c633aa\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\87d1f4dfe58076090bb2d88539c633aa\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ec6c0dbdbbf22d12e133a07aa67a308f\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ec6c0dbdbbf22d12e133a07aa67a308f\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\de784c246beadffb279447607bd137e2\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\de784c246beadffb279447607bd137e2\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\182787fe4242e9c3e9c3abe9db40fae7\transformed\soloader-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\182787fe4242e9c3e9c3abe9db40fae7\transformed\soloader-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c68acac0bc94023968ef369bb24f037d\transformed\fbcore-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c68acac0bc94023968ef369bb24f037d\transformed\fbcore-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\98e397952212d3e2136d89fae3b18646\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\98e397952212d3e2136d89fae3b18646\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\207be528686cd3afd2f06f0670a2ed41\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\207be528686cd3afd2f06f0670a2ed41\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\224821547994486db85d5b9f9d2b6143\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\224821547994486db85d5b9f9d2b6143\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bdef8dd643bd18b55d676b6b836b8035\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bdef8dd643bd18b55d676b6b836b8035\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c0bb43adce9f553621f0b37557791d2d\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c0bb43adce9f553621f0b37557791d2d\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27df90de18a571932c46b95d0e259dc7\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27df90de18a571932c46b95d0e259dc7\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d2340342a38e2ad07ed5f1cac7e16edb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d2340342a38e2ad07ed5f1cac7e16edb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cc740a720b0f76a534131973a89dee17\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cc740a720b0f76a534131973a89dee17\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\91c01c8efba535e0ad95ed10639a132e\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\91c01c8efba535e0ad95ed10639a132e\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2d77937564f5b8a89d53275d66e6c6ae\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2d77937564f5b8a89d53275d66e6c6ae\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ac2f20c823a194c54e5aa4cffebfe995\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ac2f20c823a194c54e5aa4cffebfe995\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d6076e1149a51435d3ade43e5e15b90e\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d6076e1149a51435d3ade43e5e15b90e\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\368c46131c9ae14bd68d2909a201d213\transformed\ui-core-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\368c46131c9ae14bd68d2909a201d213\transformed\ui-core-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.81.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\197577c9406afbc5eba8e8fb58fb092a\transformed\hermes-android-0.81.1-debug\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.81.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\197577c9406afbc5eba8e8fb58fb092a\transformed\hermes-android-0.81.1-debug\AndroidManifest.xml:5:5-44
MERGED from [com.polidea.rxandroidble2:rxandroidble:1.17.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c92294ba8f0f324f556f7fb61758a6c5\transformed\rxandroidble-1.17.2\AndroidManifest.xml:6:5-8:41
MERGED from [com.polidea.rxandroidble2:rxandroidble:1.17.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c92294ba8f0f324f556f7fb61758a6c5\transformed\rxandroidble-1.17.2\AndroidManifest.xml:6:5-8:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9a1fb8b6ab37a7fab4911fe988a07ef0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9a1fb8b6ab37a7fab4911fe988a07ef0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\60ccac169870d0bb8d93ec02bd82151f\transformed\tracing-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\60ccac169870d0bb8d93ec02bd82151f\transformed\tracing-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2e52a4f45d6867d7d53cffcab6f1e891\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2e52a4f45d6867d7d53cffcab6f1e891\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5d22e286bd5c783499b72cf869d67293\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5d22e286bd5c783499b72cf869d67293\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\727bb0c7cce11f5cd3a4b9879f6e0d7f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\727bb0c7cce11f5cd3a4b9879f6e0d7f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b9d230fcdfa30dbfce61bf3b9605d6b5\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b9d230fcdfa30dbfce61bf3b9605d6b5\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2aeab61e6c79580a61ba42c63c36e8b9\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2aeab61e6c79580a61ba42c63c36e8b9\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a18451f415b73841e30da63b3d9805af\transformed\fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a18451f415b73841e30da63b3d9805af\transformed\fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bcd337e69414c9658176be4bc6b3a30f\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bcd337e69414c9658176be4bc6b3a30f\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
	android:targetSdkVersion
		INJECTED from C:\BLE\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\BLE\android\app\src\debug\AndroidManifest.xml
activity#com.facebook.react.devsupport.DevSettingsActivity
ADDED from [com.facebook.react:react-android:0.81.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cba9d40536536f0777207a470c313f6b\transformed\react-android-0.81.1-debug\AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from [com.facebook.react:react-android:0.81.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cba9d40536536f0777207a470c313f6b\transformed\react-android-0.81.1-debug\AndroidManifest.xml:21:13-37
	android:name
		ADDED from [com.facebook.react:react-android:0.81.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cba9d40536536f0777207a470c313f6b\transformed\react-android-0.81.1-debug\AndroidManifest.xml:20:13-77
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\87d1f4dfe58076090bb2d88539c633aa\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d2340342a38e2ad07ed5f1cac7e16edb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d2340342a38e2ad07ed5f1cac7e16edb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9a1fb8b6ab37a7fab4911fe988a07ef0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9a1fb8b6ab37a7fab4911fe988a07ef0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\87d1f4dfe58076090bb2d88539c633aa\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\87d1f4dfe58076090bb2d88539c633aa\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\87d1f4dfe58076090bb2d88539c633aa\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\87d1f4dfe58076090bb2d88539c633aa\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\87d1f4dfe58076090bb2d88539c633aa\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\87d1f4dfe58076090bb2d88539c633aa\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\87d1f4dfe58076090bb2d88539c633aa\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c0bb43adce9f553621f0b37557791d2d\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c0bb43adce9f553621f0b37557791d2d\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c0bb43adce9f553621f0b37557791d2d\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.blescannerapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c0bb43adce9f553621f0b37557791d2d\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c0bb43adce9f553621f0b37557791d2d\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c0bb43adce9f553621f0b37557791d2d\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c0bb43adce9f553621f0b37557791d2d\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c0bb43adce9f553621f0b37557791d2d\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.blescannerapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c0bb43adce9f553621f0b37557791d2d\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c0bb43adce9f553621f0b37557791d2d\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d2340342a38e2ad07ed5f1cac7e16edb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d2340342a38e2ad07ed5f1cac7e16edb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d2340342a38e2ad07ed5f1cac7e16edb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
uses-permission-sdk-23#android.permission.ACCESS_COARSE_LOCATION
ADDED from [com.polidea.rxandroidble2:rxandroidble:1.17.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c92294ba8f0f324f556f7fb61758a6c5\transformed\rxandroidble-1.17.2\AndroidManifest.xml:18:5-88
	android:name
		ADDED from [com.polidea.rxandroidble2:rxandroidble:1.17.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c92294ba8f0f324f556f7fb61758a6c5\transformed\rxandroidble-1.17.2\AndroidManifest.xml:18:29-85
uses-permission-sdk-23#android.permission.ACCESS_FINE_LOCATION
ADDED from [com.polidea.rxandroidble2:rxandroidble:1.17.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c92294ba8f0f324f556f7fb61758a6c5\transformed\rxandroidble-1.17.2\AndroidManifest.xml:19:5-86
	android:name
		ADDED from [com.polidea.rxandroidble2:rxandroidble:1.17.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c92294ba8f0f324f556f7fb61758a6c5\transformed\rxandroidble-1.17.2\AndroidManifest.xml:19:29-83
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\27041508df8bca1a00689564c46909ec\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
meta-data#com.facebook.soloader.enabled
ADDED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bcd337e69414c9658176be4bc6b3a30f\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
	android:value
		ADDED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bcd337e69414c9658176be4bc6b3a30f\transformed\soloader-0.12.1\AndroidManifest.xml:14:13-34
	android:name
		ADDED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bcd337e69414c9658176be4bc6b3a30f\transformed\soloader-0.12.1\AndroidManifest.xml:13:13-57
