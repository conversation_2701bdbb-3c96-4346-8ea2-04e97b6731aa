ninja: Entering directory `C:\BLE\android\app\.cxx\Debug\5t2fl393\x86_64'
[0/2] Re-checking globbed directories...
[1/20] Building CXX object BlePlx_autolinked_build/CMakeFiles/react_codegen_BlePlx.dir/react/renderer/components/BlePlx/States.cpp.o
[2/20] Building CXX object BlePlx_autolinked_build/CMakeFiles/react_codegen_BlePlx.dir/react/renderer/components/BlePlx/BlePlxJSI-generated.cpp.o
[3/20] Building CXX object BlePlx_autolinked_build/CMakeFiles/react_codegen_BlePlx.dir/BlePlx-generated.cpp.o
[4/20] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/22fe2f104738b6b1b5f1b02b343e0d9a/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o
[5/20] Building CXX object BlePlx_autolinked_build/CMakeFiles/react_codegen_BlePlx.dir/react/renderer/components/BlePlx/EventEmitters.cpp.o
[6/20] Building CXX object BlePlx_autolinked_build/CMakeFiles/react_codegen_BlePlx.dir/react/renderer/components/BlePlx/Props.cpp.o
[7/20] Building CXX object BlePlx_autolinked_build/CMakeFiles/react_codegen_BlePlx.dir/react/renderer/components/BlePlx/ShadowNodes.cpp.o
[8/20] Building CXX object CMakeFiles/appmodules.dir/OnLoad.cpp.o
[9/20] Building CXX object BlePlx_autolinked_build/CMakeFiles/react_codegen_BlePlx.dir/react/renderer/components/BlePlx/ComponentDescriptors.cpp.o
[10/20] Building CXX object CMakeFiles/appmodules.dir/C_/BLE/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o
[11/20] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3b3b825b7a3fa48c4463d6d0f5aa1f15/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o
[12/20] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/f1013b39728ee71b47ca2678cb665f90/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o
[13/20] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/f1013b39728ee71b47ca2678cb665f90/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o
[14/20] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/9f9a3eea2700cf24533e0966acfc7c38/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o
[15/20] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3b3b825b7a3fa48c4463d6d0f5aa1f15/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o
[16/20] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/22fe2f104738b6b1b5f1b02b343e0d9a/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o
[17/20] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/a54f1e1d1c0df09c6ec3495181cdbad5/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o
[18/20] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/f1013b39728ee71b47ca2678cb665f90/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o
[19/20] Linking CXX shared library C:\BLE\android\app\build\intermediates\cxx\Debug\5t2fl393\obj\x86_64\libreact_codegen_safeareacontext.so
[20/20] Linking CXX shared library C:\BLE\android\app\build\intermediates\cxx\Debug\5t2fl393\obj\x86_64\libappmodules.so
